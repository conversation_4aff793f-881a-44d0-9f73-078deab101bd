<?php

namespace Tests\Feature\TikTok;

use Tests\TestCase;
use App\Models\Campaign;
use App\Models\Video;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use App\Helpers\ErrorHandler;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;

class VideoSyncServiceTest extends TestCase
{
    use RefreshDatabase;

    private VideoSyncService $videoSyncService;
    private TikTokApiService $apiService;

    protected function setUp(): void
    {
        parent::setUp();

        // Mock TikTok API service
        $this->apiService = $this->createMock(TikTokApiService::class);
        $this->videoSyncService = new VideoSyncService($this->apiService);
    }

    /** @test */
    public function it_can_sync_videos_for_campaign()
    {
        // Create test campaign
        $campaign = Campaign::factory()->create([
            'store_id' => 'test_store_123',
            'store_authorized_bc_id' => 'test_bc_456',
        ]);

        // Mock API response
        $mockApiResponse = [
            'success' => true,
            'data' => [
                'data' => [
                    'item_list' => [
                        [
                            'item_id' => '1234567890123456789',
                            'text' => 'Test video caption',
                            'spu_id_list' => ['spu_123', 'spu_456'],
                            'can_change_anchor' => true,
                            'identity_info' => [
                                'identity_id' => 'identity_123',
                                'identity_type' => 'TT_USER',
                                'display_name' => 'Test User',
                            ],
                            'video_info' => [
                                'video_id' => 'video_123',
                                'duration' => 30.5,
                                'width' => 1080,
                                'height' => 1920,
                                'format' => 'mp4',
                                'fps' => 30,
                                'size' => 1024000,
                                'preview_url' => 'https://example.com/video.mp4',
                                'video_cover_url' => 'https://example.com/thumbnail.jpg',
                            ],
                        ],
                    ],
                    'page_info' => [
                        'page' => 1,
                        'page_size' => 50,
                        'total_number' => 1,
                        'total_page' => 1,
                    ],
                ],
            ],
        ];

        $this->apiService
            ->expects($this->once())
            ->method('getVideos')
            ->willReturn($mockApiResponse);

        // Execute sync
        $result = $this->videoSyncService->syncVideosForCampaign($campaign);

        // Assert success
        $this->assertTrue(ErrorHandler::isSuccess($result));
        $this->assertEquals(1, $result['data']['stats']['total']);
        $this->assertEquals(1, $result['data']['stats']['created']);

        // Assert video was created in database
        $this->assertDatabaseHas('gmv_max_videos', [
            'video_id' => '1234567890123456789',
            'campaign_id' => $campaign->campaign_id,
            'title' => 'Test video caption',
            'duration' => 30,
            'can_change_anchor' => true,
        ]);

        $video = Video::where('video_id', '1234567890123456789')->first();
        $this->assertNotNull($video);
        $this->assertEquals(['spu_123', 'spu_456'], $video->spu_id_list);
        $this->assertEquals('TT_USER', $video->identity_info['identity_type']);
        $this->assertEquals('Test User', $video->identity_info['display_name']);
    }

    /** @test */
    public function it_handles_campaign_missing_store_info()
    {
        // Create campaign without required store info
        $campaign = Campaign::factory()->create([
            'store_id' => null,
            'store_authorized_bc_id' => null,
        ]);

        $result = $this->videoSyncService->syncVideosForCampaign($campaign);

        // Assert error
        $this->assertFalse(ErrorHandler::isSuccess($result));
        $this->assertStringContains('missing required store information', $result['message']);
    }

    /** @test */
    public function it_handles_api_errors()
    {
        $campaign = Campaign::factory()->create([
            'store_id' => 'test_store_123',
            'store_authorized_bc_id' => 'test_bc_456',
        ]);

        // Mock API error response
        $mockErrorResponse = [
            'success' => false,
            'message' => 'API Error: Invalid access token',
            'error_type' => 'authentication_error',
        ];

        $this->apiService
            ->expects($this->once())
            ->method('getVideos')
            ->willReturn($mockErrorResponse);

        $result = $this->videoSyncService->syncVideosForCampaign($campaign);

        // Assert error is propagated
        $this->assertFalse(ErrorHandler::isSuccess($result));
        $this->assertEquals($mockErrorResponse, $result);
    }

    /** @test */
    public function it_updates_existing_videos()
    {
        $campaign = Campaign::factory()->create([
            'store_id' => 'test_store_123',
            'store_authorized_bc_id' => 'test_bc_456',
        ]);

        // Create existing video
        $existingVideo = Video::factory()->create([
            'video_id' => '1234567890123456789',
            'campaign_id' => $campaign->campaign_id,
            'title' => 'Old title',
            'duration' => 20,
        ]);

        // Mock API response with updated data
        $mockApiResponse = [
            'success' => true,
            'data' => [
                'data' => [
                    'item_list' => [
                        [
                            'item_id' => '1234567890123456789',
                            'text' => 'Updated video caption',
                            'spu_id_list' => ['spu_789'],
                            'can_change_anchor' => false,
                            'identity_info' => [
                                'identity_id' => 'identity_456',
                                'identity_type' => 'BC_AUTH_TT',
                                'display_name' => 'Updated User',
                            ],
                            'video_info' => [
                                'video_id' => 'video_456',
                                'duration' => 45.0,
                                'width' => 720,
                                'height' => 1280,
                                'format' => 'mp4',
                                'fps' => 24,
                                'size' => 2048000,
                                'preview_url' => 'https://example.com/updated_video.mp4',
                                'video_cover_url' => 'https://example.com/updated_thumbnail.jpg',
                            ],
                        ],
                    ],
                    'page_info' => [
                        'page' => 1,
                        'page_size' => 50,
                        'total_number' => 1,
                        'total_page' => 1,
                    ],
                ],
            ],
        ];

        $this->apiService
            ->expects($this->once())
            ->method('getVideos')
            ->willReturn($mockApiResponse);

        $result = $this->videoSyncService->syncVideosForCampaign($campaign);

        // Assert success with update
        $this->assertTrue(ErrorHandler::isSuccess($result));
        $this->assertEquals(1, $result['data']['stats']['total']);
        $this->assertEquals(0, $result['data']['stats']['created']);
        $this->assertEquals(1, $result['data']['stats']['updated']);

        // Assert video was updated
        $existingVideo->refresh();
        $this->assertEquals('Updated video caption', $existingVideo->title);
        $this->assertEquals(45, $existingVideo->duration);
        $this->assertEquals(['spu_789'], $existingVideo->spu_id_list);
        $this->assertEquals('BC_AUTH_TT', $existingVideo->identity_info['identity_type']);
        $this->assertFalse($existingVideo->can_change_anchor);
    }

    /** @test */
    public function it_can_sync_multiple_campaigns()
    {
        // Create test campaigns
        $campaign1 = Campaign::factory()->create([
            'store_id' => 'store_1',
            'store_authorized_bc_id' => 'bc_1',
        ]);

        $campaign2 = Campaign::factory()->create([
            'store_id' => 'store_2',
            'store_authorized_bc_id' => 'bc_2',
        ]);

        // Mock successful API responses
        $this->apiService
            ->expects($this->exactly(2))
            ->method('getVideos')
            ->willReturn([
                'success' => true,
                'data' => [
                    'data' => [
                        'item_list' => [
                            [
                                'item_id' => '1111111111111111111',
                                'text' => 'Video 1',
                                'spu_id_list' => [],
                                'can_change_anchor' => true,
                                'identity_info' => [],
                                'video_info' => ['duration' => 30],
                            ],
                        ],
                        'page_info' => ['page' => 1, 'total_page' => 1],
                    ],
                ],
            ]);

        $result = $this->videoSyncService->syncVideosForMultipleCampaigns([
            $campaign1->id,
            $campaign2->id,
        ]);

        // Assert overall stats
        $this->assertEquals(2, $result['overall_stats']['campaigns_processed']);
        $this->assertEquals(2, $result['overall_stats']['campaigns_success']);
        $this->assertEquals(0, $result['overall_stats']['campaigns_failed']);
        $this->assertEquals(2, $result['overall_stats']['total_videos']);
        $this->assertEquals(2, $result['overall_stats']['total_created']);
    }
}
