<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Shop;

class SampleProductsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get first shop for testing
        $shop = Shop::first();
        
        if (!$shop) {
            $this->command->error('No shops found. Please seed shops first.');
            return;
        }

        $sampleProducts = [
            [
                'item_group_id' => 'SPU_001_TEST',
                'store_id' => $shop->shop_id,
                'shop_id' => $shop->id,
                'title' => 'Áo thun cotton cao cấp',
                'product_image_url' => 'https://example.com/image1.jpg',
                'min_price' => 150000,
                'max_price' => 250000,
                'currency' => 'VND',
                'category' => 'Fashion',
                'historical_sales' => 150,
                'status' => 'AVAILABLE',
                'api_status' => 'AVAILABLE',
                'gmv_max_ads_status' => 'UNOCCUPIED',
                'is_running_custom_shop_ads' => false,
            ],
            [
                'item_group_id' => 'SPU_002_TEST',
                'store_id' => $shop->shop_id,
                'shop_id' => $shop->id,
                'title' => 'Kem dưỡng da mặt vitamin C',
                'product_image_url' => 'https://example.com/image2.jpg',
                'min_price' => 299000,
                'max_price' => 399000,
                'currency' => 'VND',
                'category' => 'Beauty',
                'historical_sales' => 89,
                'status' => 'AVAILABLE',
                'api_status' => 'AVAILABLE',
                'gmv_max_ads_status' => 'OCCUPIED',
                'is_running_custom_shop_ads' => false,
            ],
            [
                'item_group_id' => 'SPU_003_TEST',
                'store_id' => $shop->shop_id,
                'shop_id' => $shop->id,
                'title' => 'Tai nghe bluetooth không dây',
                'product_image_url' => 'https://example.com/image3.jpg',
                'min_price' => 500000,
                'max_price' => 800000,
                'currency' => 'VND',
                'category' => 'Electronics',
                'historical_sales' => 234,
                'status' => 'AVAILABLE',
                'api_status' => 'NOT_AVAILABLE',
                'gmv_max_ads_status' => 'UNOCCUPIED',
                'is_running_custom_shop_ads' => true,
            ],
            [
                'item_group_id' => 'SPU_004_TEST',
                'store_id' => $shop->shop_id,
                'shop_id' => $shop->id,
                'title' => 'Bình giữ nhiệt inox 500ml',
                'product_image_url' => 'https://example.com/image4.jpg',
                'min_price' => 120000,
                'max_price' => 180000,
                'currency' => 'VND',
                'category' => 'Home',
                'historical_sales' => 67,
                'status' => 'AVAILABLE',
                'api_status' => 'AVAILABLE',
                'gmv_max_ads_status' => 'UNOCCUPIED',
                'is_running_custom_shop_ads' => false,
            ],
            [
                'item_group_id' => 'SPU_005_TEST',
                'store_id' => $shop->shop_id,
                'shop_id' => $shop->id,
                'title' => 'Giày thể thao nam nữ',
                'product_image_url' => 'https://example.com/image5.jpg',
                'min_price' => 450000,
                'max_price' => 650000,
                'currency' => 'VND',
                'category' => 'Sports',
                'historical_sales' => 312,
                'status' => 'AVAILABLE',
                'api_status' => 'AVAILABLE',
                'gmv_max_ads_status' => 'UNOCCUPIED',
                'is_running_custom_shop_ads' => false,
            ],
        ];

        foreach ($sampleProducts as $productData) {
            Product::create($productData);
        }

        $this->command->info('Created ' . count($sampleProducts) . ' sample products successfully!');
    }
}
