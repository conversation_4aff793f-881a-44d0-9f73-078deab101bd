<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gmv_max_videos', function (Blueprint $table) {
            // Drop existing foreign key constraint
            $table->dropForeign(['campaign_id']);

            // Change campaign_id from bigint to string to match campaigns.campaign_id
            $table->string('campaign_id')->change();

            // Add new foreign key constraint that references campaign_id field
            $table->foreign('campaign_id')
                  ->references('campaign_id')
                  ->on('gmv_max_campaigns')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gmv_max_videos', function (Blueprint $table) {
            // Drop the new foreign key constraint
            $table->dropForeign(['campaign_id']);

            // Change campaign_id back to bigint (foreignId)
            $table->unsignedBigInteger('campaign_id')->change();

            // Restore original foreign key constraint (references id)
            $table->foreign('campaign_id')
                  ->references('id')
                  ->on('gmv_max_campaigns')
                  ->onDelete('cascade');
        });
    }
};
