<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('campaign_reports', function (Blueprint $table) {
            // Drop existing foreign key constraint if it exists
            $this->dropForeignKeyIfExists('campaign_reports', 'campaign_reports_campaign_id_foreign');

            // Change campaign_id to string type if it's not already
            $table->string('campaign_id')->change();

            // Add new foreign key constraint that references campaign_id field
            $table->foreign('campaign_id')
                  ->references('campaign_id')
                  ->on('gmv_max_campaigns')
                  ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('campaign_reports', function (Blueprint $table) {
            // Drop the new foreign key constraint if it exists
            $this->dropForeignKeyIfExists('campaign_reports', 'campaign_reports_campaign_id_foreign');

            // Restore original foreign key constraint (references id) only if gmv_max_campaigns.id exists
            if ($this->columnExists('gmv_max_campaigns', 'id')) {
                $table->foreign('campaign_id')
                      ->references('id')
                      ->on('gmv_max_campaigns')
                      ->onDelete('cascade');
            }
        });
    }

    /**
     * Check if a foreign key constraint exists and drop it
     */
    private function dropForeignKeyIfExists(string $table, string $constraintName): void
    {
        $database = DB::connection()->getDatabaseName();

        $exists = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = ?
            AND TABLE_NAME = ?
            AND CONSTRAINT_NAME = ?
        ", [$database, $table, $constraintName]);

        if (!empty($exists)) {
            DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraintName}`");
        }
    }

    /**
     * Check if a column exists in a table
     */
    private function columnExists(string $table, string $column): bool
    {
        return Schema::hasColumn($table, $column);
    }
};
