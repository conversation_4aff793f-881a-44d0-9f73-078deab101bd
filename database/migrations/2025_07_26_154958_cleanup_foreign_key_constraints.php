<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Clean up any inconsistent foreign key constraints
     */
    public function up(): void
    {
        // List of potential foreign key constraints that might exist
        $constraintsToCleanup = [
            'campaign_reports' => [
                'campaign_reports_campaign_id_foreign',
            ],
            'gmv_max_videos' => [
                'gmv_max_videos_campaign_id_foreign',
            ],
            'gmv_max_sessions' => [
                'gmv_max_sessions_campaign_id_foreign',
            ],
        ];

        foreach ($constraintsToCleanup as $table => $constraints) {
            foreach ($constraints as $constraint) {
                $this->dropForeignKeyIfExists($table, $constraint);
            }
        }

        // Now ensure all tables have the correct column types and constraints
        $this->ensureCorrectStructure();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a cleanup migration, no need to reverse
    }

    /**
     * Ensure all tables have the correct structure
     */
    private function ensureCorrectStructure(): void
    {
        // Fix campaign_reports table
        if (Schema::hasTable('campaign_reports')) {
            Schema::table('campaign_reports', function (Blueprint $table) {
                // Ensure campaign_id is string type
                if (Schema::hasColumn('campaign_reports', 'campaign_id')) {
                    $table->string('campaign_id')->change();
                }
            });

            // Add foreign key constraint if it doesn't exist
            if (!$this->foreignKeyExists('campaign_reports', 'campaign_reports_campaign_id_foreign')) {
                Schema::table('campaign_reports', function (Blueprint $table) {
                    $table->foreign('campaign_id')
                          ->references('campaign_id')
                          ->on('gmv_max_campaigns')
                          ->onDelete('cascade');
                });
            }
        }

        // Fix gmv_max_videos table
        if (Schema::hasTable('gmv_max_videos')) {
            Schema::table('gmv_max_videos', function (Blueprint $table) {
                // Ensure campaign_id is string type
                if (Schema::hasColumn('gmv_max_videos', 'campaign_id')) {
                    $table->string('campaign_id')->change();
                }
            });

            // Add foreign key constraint if it doesn't exist
            if (!$this->foreignKeyExists('gmv_max_videos', 'gmv_max_videos_campaign_id_foreign')) {
                Schema::table('gmv_max_videos', function (Blueprint $table) {
                    $table->foreign('campaign_id')
                          ->references('campaign_id')
                          ->on('gmv_max_campaigns')
                          ->onDelete('cascade');
                });
            }
        }

        // Fix gmv_max_sessions table
        if (Schema::hasTable('gmv_max_sessions')) {
            Schema::table('gmv_max_sessions', function (Blueprint $table) {
                // Ensure campaign_id is string type
                if (Schema::hasColumn('gmv_max_sessions', 'campaign_id')) {
                    $table->string('campaign_id')->change();
                }
            });

            // Add foreign key constraint if it doesn't exist
            if (!$this->foreignKeyExists('gmv_max_sessions', 'gmv_max_sessions_campaign_id_foreign')) {
                Schema::table('gmv_max_sessions', function (Blueprint $table) {
                    $table->foreign('campaign_id')
                          ->references('campaign_id')
                          ->on('gmv_max_campaigns')
                          ->onDelete('cascade');
                });
            }
        }
    }

    /**
     * Check if a foreign key constraint exists and drop it
     */
    private function dropForeignKeyIfExists(string $table, string $constraintName): void
    {
        if ($this->foreignKeyExists($table, $constraintName)) {
            DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraintName}`");
        }
    }

    /**
     * Check if a foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $constraintName): bool
    {
        $database = DB::connection()->getDatabaseName();

        $exists = DB::select("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_SCHEMA = ?
            AND TABLE_NAME = ?
            AND CONSTRAINT_NAME = ?
        ", [$database, $table, $constraintName]);

        return !empty($exists);
    }
};
