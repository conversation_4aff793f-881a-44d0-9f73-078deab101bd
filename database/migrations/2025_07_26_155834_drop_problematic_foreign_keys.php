<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     * Drop all problematic foreign key constraints before other migrations run
     */
    public function up(): void
    {
        // List of all foreign key constraints that might cause issues
        $problematicConstraints = [
            'campaign_reports' => [
                'campaign_reports_campaign_id_foreign',
            ],
            'gmv_max_videos' => [
                'gmv_max_videos_campaign_id_foreign',
            ],
            'gmv_max_sessions' => [
                'gmv_max_sessions_campaign_id_foreign',
            ],
        ];

        foreach ($problematicConstraints as $table => $constraints) {
            if (Schema::hasTable($table)) {
                foreach ($constraints as $constraint) {
                    $this->dropForeignKeyIfExists($table, $constraint);
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // This is a cleanup migration, no need to reverse
        // The subsequent migrations will handle creating the correct constraints
    }

    /**
     * Check if a foreign key constraint exists and drop it
     */
    private function dropForeignKeyIfExists(string $table, string $constraintName): void
    {
        if ($this->foreignKeyExists($table, $constraintName)) {
            try {
                DB::statement("ALTER TABLE `{$table}` DROP FOREIGN KEY `{$constraintName}`");
            } catch (\Exception $e) {
                // Ignore errors if constraint doesn't exist or can't be dropped
            }
        }
    }

    /**
     * Check if a foreign key constraint exists
     */
    private function foreignKeyExists(string $table, string $constraintName): bool
    {
        try {
            $database = DB::connection()->getDatabaseName();

            $exists = DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = ?
                AND TABLE_NAME = ?
                AND CONSTRAINT_NAME = ?
            ", [$database, $table, $constraintName]);

            return !empty($exists);
        } catch (\Exception $e) {
            return false;
        }
    }
};
