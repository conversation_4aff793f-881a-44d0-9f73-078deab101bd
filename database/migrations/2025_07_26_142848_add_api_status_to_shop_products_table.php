<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            // Add api_status column to match TikTok API response
            $table->enum('api_status', ['AVAILABLE', 'NOT_AVAILABLE'])
                  ->default('AVAILABLE')
                  ->after('status')
                  ->comment('Product status from TikTok API');

            // Add index for performance
            $table->index('api_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shop_products', function (Blueprint $table) {
            $table->dropIndex(['api_status']);
            $table->dropColumn('api_status');
        });
    }
};
