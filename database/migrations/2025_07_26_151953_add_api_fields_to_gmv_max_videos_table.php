<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('gmv_max_videos', function (Blueprint $table) {
            // Add new fields from TikTok API response
            $table->json('spu_id_list')->nullable()->comment('List of Product SPU IDs associated with the video');
            $table->json('identity_info')->nullable()->comment('Information about the identity associated with the video');
            $table->json('video_info')->nullable()->comment('Detailed video information from TikTok API');
            $table->boolean('can_change_anchor')->default(false)->comment('Whether you can change the product anchor link');

            // Add indexes for better performance
            $table->index(['can_change_anchor']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('gmv_max_videos', function (Blueprint $table) {
            $table->dropColumn([
                'spu_id_list',
                'identity_info',
                'video_info',
                'can_change_anchor'
            ]);
        });
    }
};
