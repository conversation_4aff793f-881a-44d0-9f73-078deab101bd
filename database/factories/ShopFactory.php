<?php

namespace Database\Factories;

use App\Models\Shop;
use App\Models\AdvertiserAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shop>
 */
class ShopFactory extends Factory
{
    protected $model = Shop::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'shop_id' => 'shop_' . $this->faker->randomNumber(8),
            'name' => $this->faker->company(),
            'region' => $this->faker->randomElement(['VN', 'TH', 'MY', 'SG', 'ID', 'PH']),
            'status' => $this->faker->randomElement(['active', 'inactive', 'suspended', 'pending']),
            'is_eligible_gmv_max' => $this->faker->boolean(80),
            'currency' => $this->faker->randomElement(['VND', 'USD', 'THB', 'MYR', 'SGD', 'IDR', 'PHP']),
            'advertiser_id' => $this->faker->numerify('###################'),
            'advertiser_account_id' => AdvertiserAccount::factory(),
            'exclusive_authorization_status' => $this->faker->randomElement(['none', 'pending', 'granted']),
        ];
    }

    /**
     * Indicate that the shop is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the shop supports GMV Max.
     */
    public function gmvMaxAvailable(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_eligible_gmv_max' => true,
        ]);
    }

    /**
     * Set specific region.
     */
    public function withRegion(string $region): static
    {
        return $this->state(fn (array $attributes) => [
            'region' => $region,
        ]);
    }
}
