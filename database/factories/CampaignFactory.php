<?php

namespace Database\Factories;

use App\Models\Campaign;
use App\Models\Shop;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Campaign>
 */
class CampaignFactory extends Factory
{
    protected $model = Campaign::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = $this->faker->dateTimeBetween('-30 days', '+30 days');
        $endDate = $this->faker->dateTimeBetween($startDate, '+90 days');

        return [
            'campaign_id' => $this->faker->numerify('###################'), // 19 digit number
            'name' => $this->faker->words(3, true) . ' Campaign',
            'status' => $this->faker->randomElement(['active', 'inactive', 'paused']),
            'target_roi' => $this->faker->randomFloat(2, 1.5, 5.0),
            'budget' => $this->faker->randomFloat(2, 100, 10000),
            'daily_budget' => $this->faker->randomFloat(2, 10, 500),
            'start_date' => $startDate,
            'end_date' => $endDate,
            'advertiser_id' => $this->faker->numerify('###################'),
            'shop_id' => Shop::factory(),
            'store_id' => 'store_' . $this->faker->randomNumber(8),
            'store_authorized_bc_id' => 'bc_' . $this->faker->randomNumber(8),
        ];
    }

    /**
     * Indicate that the campaign is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the campaign is paused.
     */
    public function paused(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paused',
        ]);
    }

    /**
     * Set specific store information.
     */
    public function withStore(string $storeId, string $bcId): static
    {
        return $this->state(fn (array $attributes) => [
            'store_id' => $storeId,
            'store_authorized_bc_id' => $bcId,
        ]);
    }

    /**
     * Set specific advertiser ID.
     */
    public function withAdvertiser(string $advertiserId): static
    {
        return $this->state(fn (array $attributes) => [
            'advertiser_id' => $advertiserId,
        ]);
    }

    /**
     * Set specific budget.
     */
    public function withBudget(float $totalBudget, float $dailyBudget): static
    {
        return $this->state(fn (array $attributes) => [
            'budget' => $totalBudget,
            'daily_budget' => $dailyBudget,
            'budget_details' => $totalBudget,
        ]);
    }

    /**
     * Set specific ROI target.
     */
    public function withRoi(float $targetRoi, float $roasBid): static
    {
        return $this->state(fn (array $attributes) => [
            'target_roi' => $targetRoi,
            'roas_bid' => $roasBid,
            'roi_protection_enabled' => true,
        ]);
    }

    /**
     * Set specific product SPU IDs.
     */
    public function withProducts(array $spuIds): static
    {
        return $this->state(fn (array $attributes) => [
            'item_group_ids' => $spuIds,
            'product_specific_type' => 'SPECIFIC_PRODUCTS',
        ]);
    }

    /**
     * Set specific identity list.
     */
    public function withIdentities(array $identities): static
    {
        return $this->state(fn (array $attributes) => [
            'identity_list' => $identities,
        ]);
    }

    /**
     * Create campaign for all products.
     */
    public function allProducts(): static
    {
        return $this->state(fn (array $attributes) => [
            'product_specific_type' => 'ALL_PRODUCTS',
            'item_group_ids' => [],
        ]);
    }

    /**
     * Create campaign with authorized posts.
     */
    public function authorizedPosts(): static
    {
        return $this->state(fn (array $attributes) => [
            'product_video_specific_type' => 'AUTHORIZED_POSTS',
        ]);
    }

    /**
     * Create campaign with customized posts.
     */
    public function customizedPosts(): static
    {
        return $this->state(fn (array $attributes) => [
            'product_video_specific_type' => 'CUSTOMIZED_POSTS',
        ]);
    }
}
