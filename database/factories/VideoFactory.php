<?php

namespace Database\Factories;

use App\Models\Video;
use App\Models\Campaign;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Video>
 */
class VideoFactory extends Factory
{
    protected $model = Video::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'video_id' => $this->faker->numerify('###################'), // 19 digit number
            'campaign_id' => $this->faker->numerify('###################'), // Will be overridden when used with specific campaign
            'title' => $this->faker->sentence(6),
            'url' => $this->faker->url(),
            'thumbnail' => $this->faker->imageUrl(640, 480, 'video'),
            'duration' => $this->faker->numberBetween(10, 300), // 10 seconds to 5 minutes
            'status' => $this->faker->randomElement(['active', 'inactive', 'processing', 'failed']),
            'is_custom_anchor' => $this->faker->boolean(30), // 30% chance of being custom anchor
            'can_change_anchor' => $this->faker->boolean(50),
            'spu_id_list' => $this->faker->randomElements([
                'spu_' . $this->faker->randomNumber(6),
                'spu_' . $this->faker->randomNumber(6),
                'spu_' . $this->faker->randomNumber(6),
            ], $this->faker->numberBetween(0, 3)),
            'identity_info' => [
                'identity_id' => 'identity_' . $this->faker->randomNumber(8),
                'identity_type' => $this->faker->randomElement(['TT_USER', 'BC_AUTH_TT', 'TTS_TT', 'AUTH_CODE']),
                'display_name' => $this->faker->userName(),
                'profile_image' => $this->faker->imageUrl(100, 100, 'people'),
            ],
            'video_info' => [
                'video_id' => 'video_' . $this->faker->randomNumber(8),
                'width' => $this->faker->randomElement([720, 1080, 1920]),
                'height' => $this->faker->randomElement([1280, 1920, 1080]),
                'duration' => $this->faker->randomFloat(2, 10, 300),
                'format' => 'mp4',
                'fps' => $this->faker->randomElement([24, 30, 60]),
                'size' => $this->faker->numberBetween(500000, 50000000), // 500KB to 50MB
                'bit_rate' => $this->faker->numberBetween(1000000, 10000000),
                'definition' => $this->faker->randomElement(['720p', '1080p', '1440p']),
                'signature' => $this->faker->md5(),
                'preview_url' => $this->faker->url(),
                'video_cover_url' => $this->faker->imageUrl(640, 480, 'video'),
            ],
        ];
    }

    /**
     * Indicate that the video is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the video is a custom anchor.
     */
    public function customAnchor(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_custom_anchor' => true,
            'can_change_anchor' => true,
        ]);
    }

    /**
     * Indicate that the video can change anchor.
     */
    public function canChangeAnchor(): static
    {
        return $this->state(fn (array $attributes) => [
            'can_change_anchor' => true,
        ]);
    }

    /**
     * Set specific identity type.
     */
    public function withIdentityType(string $identityType): static
    {
        return $this->state(function (array $attributes) use ($identityType) {
            $identityInfo = $attributes['identity_info'];
            $identityInfo['identity_type'] = $identityType;

            // Add conditional fields based on identity type
            switch ($identityType) {
                case 'BC_AUTH_TT':
                    $identityInfo['identity_authorized_bc_id'] = 'bc_' . $this->faker->randomNumber(8);
                    break;
                case 'TTS_TT':
                    $identityInfo['store_id'] = 'store_' . $this->faker->randomNumber(8);
                    break;
            }

            return ['identity_info' => $identityInfo];
        });
    }

    /**
     * Set specific SPU IDs.
     */
    public function withSpuIds(array $spuIds): static
    {
        return $this->state(fn (array $attributes) => [
            'spu_id_list' => $spuIds,
        ]);
    }

    /**
     * Set specific duration.
     */
    public function withDuration(int $duration): static
    {
        return $this->state(function (array $attributes) use ($duration) {
            $videoInfo = $attributes['video_info'];
            $videoInfo['duration'] = (float) $duration;

            return [
                'duration' => $duration,
                'video_info' => $videoInfo,
            ];
        });
    }

    /**
     * Set specific video resolution.
     */
    public function withResolution(int $width, int $height): static
    {
        return $this->state(function (array $attributes) use ($width, $height) {
            $videoInfo = $attributes['video_info'];
            $videoInfo['width'] = $width;
            $videoInfo['height'] = $height;
            $videoInfo['definition'] = $height . 'p';

            return ['video_info' => $videoInfo];
        });
    }

    /**
     * Create a long video (>= 60 seconds).
     */
    public function longVideo(): static
    {
        $duration = $this->faker->numberBetween(60, 600); // 1-10 minutes
        return $this->withDuration($duration);
    }

    /**
     * Create a short video (< 60 seconds).
     */
    public function shortVideo(): static
    {
        $duration = $this->faker->numberBetween(10, 59);
        return $this->withDuration($duration);
    }
}
