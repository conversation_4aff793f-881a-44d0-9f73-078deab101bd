<?php

namespace App\Services\TikTok;

use App\Models\Video;
use App\Models\Campaign;
use App\Models\Shop;
use App\Models\Identity;
use App\Helpers\ErrorHandler;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Exception;

/**
 * Service chuyên biệt để đồng bộ video data từ TikTok API
 */
class VideoSyncService
{
    private TikTokApiService $apiService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Sync videos for a specific campaign
     *
     * @param Campaign $campaign Campaign to sync videos for
     * @param array $options Additional options for video retrieval
     * @return array Sync result with statistics
     */
    public function syncVideosForCampaign(Campaign $campaign, array $options = []): array
    {
        try {
            // Validate campaign has required data
            if (!$campaign->store_id || !$campaign->store_authorized_bc_id) {
                return ErrorHandler::createErrorResponse(
                    'Campaign missing required store information',
                    ErrorHandler::VALIDATION_ERROR,
                    ['campaign_id' => $campaign->campaign_id]
                );
            }

            // Build API options
            $apiOptions = array_merge([
                'store_id' => $campaign->store_id,
                'store_authorized_bc_id' => $campaign->store_authorized_bc_id,
                'page' => 1,
                'page_size' => 50,
            ], $options);

            // Add identity list if available in campaign
            if ($campaign->identity_list && is_array($campaign->identity_list)) {
                $apiOptions['identity_list'] = $campaign->identity_list;
            }

            // Add product filters if available
            if ($campaign->item_group_ids && is_array($campaign->item_group_ids)) {
                $apiOptions['spu_id_list'] = array_slice($campaign->item_group_ids, 0, 50);
            }

            $allVideos = [];
            $currentPage = 1;
            $totalPages = 1;

            // Fetch all pages of videos
            do {
                $apiOptions['page'] = $currentPage;
                $response = $this->apiService->getVideos($apiOptions);

                if (!ErrorHandler::isSuccess($response)) {
                    return $response;
                }

                $data = $response['data']['data'] ?? [];
                $pageInfo = $data['page_info'] ?? [];

                $videos = $data['item_list'] ?? [];
                $allVideos = array_merge($allVideos, $videos);

                $totalPages = $pageInfo['total_page'] ?? 1;
                $currentPage++;

            } while ($currentPage <= $totalPages && $currentPage <= 10); // Limit to 10 pages max

            // Process and save videos
            return $this->processAndSaveVideos($campaign, $allVideos);

        } catch (Exception $e) {
            Log::error('Failed to sync videos for campaign', [
                'campaign_id' => $campaign->campaign_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return ErrorHandler::createErrorResponse(
                'Video sync failed: ' . $e->getMessage(),
                ErrorHandler::SYSTEM_ERROR,
                ['campaign_id' => $campaign->campaign_id]
            );
        }
    }

    /**
     * Process and save videos to database
     *
     * @param Campaign $campaign Campaign the videos belong to
     * @param array $videos Array of video data from API
     * @return array Processing result with statistics
     */
    private function processAndSaveVideos(Campaign $campaign, array $videos): array
    {
        $stats = [
            'total' => count($videos),
            'created' => 0,
            'updated' => 0,
            'skipped' => 0,
            'errors' => 0
        ];

        DB::beginTransaction();

        try {
            foreach ($videos as $videoData) {
                $result = $this->processSingleVideo($campaign, $videoData);
                $stats[$result]++;
            }

            DB::commit();

            return ErrorHandler::createSuccessResponse([
                'stats' => $stats,
                'campaign_id' => $campaign->campaign_id
            ], "Successfully processed {$stats['total']} videos");

        } catch (Exception $e) {
            DB::rollBack();

            Log::error('Failed to process videos', [
                'campaign_id' => $campaign->campaign_id,
                'error' => $e->getMessage()
            ]);

            return ErrorHandler::createErrorResponse(
                'Failed to save videos: ' . $e->getMessage(),
                ErrorHandler::DATABASE_ERROR,
                ['campaign_id' => $campaign->campaign_id, 'stats' => $stats]
            );
        }
    }

    /**
     * Process a single video and save to database
     *
     * @param Campaign $campaign Campaign the video belongs to
     * @param array $videoData Video data from API
     * @return string Result type: 'created', 'updated', 'skipped', or 'errors'
     */
    private function processSingleVideo(Campaign $campaign, array $videoData): string
    {
        try {
            $itemId = $videoData['item_id'] ?? null;
            if (!$itemId) {
                Log::warning('Video missing item_id', ['video_data' => $videoData]);
                return 'skipped';
            }

            // Extract video information
            $videoInfo = $videoData['video_info'] ?? [];
            $identityInfo = $videoData['identity_info'] ?? [];

            // Prepare video data for database
            $videoDbData = [
                'video_id' => $itemId,
                'campaign_id' => $campaign->campaign_id,
                'title' => $videoData['text'] ?? '',
                'url' => $videoInfo['preview_url'] ?? '',
                'thumbnail' => $videoInfo['video_cover_url'] ?? '',
                'duration' => (int)($videoInfo['duration'] ?? 0),
                'status' => 'active',
                'is_custom_anchor' => $videoData['can_change_anchor'] ?? false,
                // Additional fields from API
                'spu_id_list' => json_encode($videoData['spu_id_list'] ?? []),
                'identity_info' => json_encode($identityInfo),
                'video_info' => json_encode($videoInfo),
            ];

            // Check if video already exists
            $existingVideo = Video::where('video_id', $itemId)
                ->where('campaign_id', $campaign->campaign_id)
                ->first();

            if ($existingVideo) {
                $existingVideo->update($videoDbData);
                return 'updated';
            } else {
                Video::create($videoDbData);
                return 'created';
            }

        } catch (Exception $e) {
            Log::error('Failed to process single video', [
                'campaign_id' => $campaign->campaign_id,
                'video_data' => $videoData,
                'error' => $e->getMessage()
            ]);
            return 'errors';
        }
    }

    /**
     * Sync videos for multiple campaigns
     *
     * @param array $campaignIds Array of campaign IDs to sync
     * @param array $options Additional options for video retrieval
     * @return array Sync result with statistics for all campaigns
     */
    public function syncVideosForMultipleCampaigns(array $campaignIds, array $options = []): array
    {
        $results = [];
        $overallStats = [
            'campaigns_processed' => 0,
            'campaigns_success' => 0,
            'campaigns_failed' => 0,
            'total_videos' => 0,
            'total_created' => 0,
            'total_updated' => 0,
        ];

        foreach ($campaignIds as $campaignId) {
            $campaign = Campaign::find($campaignId);
            if (!$campaign) {
                $results[$campaignId] = [
                    'success' => false,
                    'error' => 'Campaign not found'
                ];
                $overallStats['campaigns_failed']++;
                continue;
            }

            $result = $this->syncVideosForCampaign($campaign, $options);
            $results[$campaignId] = $result;
            $overallStats['campaigns_processed']++;

            if (ErrorHandler::isSuccess($result)) {
                $overallStats['campaigns_success']++;
                $stats = $result['data']['stats'] ?? [];
                $overallStats['total_videos'] += $stats['total'] ?? 0;
                $overallStats['total_created'] += $stats['created'] ?? 0;
                $overallStats['total_updated'] += $stats['updated'] ?? 0;
            } else {
                $overallStats['campaigns_failed']++;
            }
        }

        return [
            'overall_stats' => $overallStats,
            'campaign_results' => $results
        ];
    }
}
