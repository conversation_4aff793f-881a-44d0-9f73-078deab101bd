<?php

namespace App\Services\TikTok;

use App\Helpers\ErrorHandler;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use JsonException;
use RuntimeException;

/**
 * TikTok Business API Service
 * Handles all interactions with TikTok GMV Max API endpoints
 */
class TikTokApiService
{
    protected string $baseUrl;
    protected ?string $accessToken;
    protected ?string $advertiserId;
    protected ?string $appId;
    protected ?string $appSecret;
    protected int $rateLimitDelay = 1; // seconds between requests
    protected TikTokConfigService $configService;
    protected ?array $lastError = null;

    public function __construct()
    {
        $this->configService = new TikTokConfigService();

        // Load config từ hybrid system (file + database)
        $this->baseUrl = $this->configService->get('api.base_url', 'https://business-api.tiktok.com');
        $this->accessToken = $this->configService->get('api.access_token');
        $this->appId = $this->configService->get('api.app_id');
        $this->appSecret = $this->configService->get('api.secret');

        // Debug logging để kiểm tra giá trị thực tế
        Log::debug('TikTok API Service config values', [
            'access_token_length' => strlen($this->accessToken ?? ''),
            'app_id_length' => strlen($this->appId ?? ''),
            'app_secret_length' => strlen($this->appSecret ?? ''),
            'access_token_empty' => empty($this->accessToken),
            'app_id_empty' => empty($this->appId),
            'app_secret_empty' => empty($this->appSecret),
        ]);

        // Note: advertiserId will be set dynamically based on context
        // For now, try to get default from config
        $this->advertiserId = $this->configService->get('api.advertiser_id');

        // Production mode: Validate all required configuration
        $missingConfig = [];

        if (empty($this->accessToken)) {
            $missingConfig[] = 'Access Token';
        }

        if (empty($this->appId)) {
            $missingConfig[] = 'App ID';
        }

        if (empty($this->appSecret)) {
            $missingConfig[] = 'App Secret';
        }

        if (!empty($missingConfig)) {
            // Thử rebuild cache trước khi báo lỗi
            Log::warning('TikTok API configuration missing, attempting to rebuild cache', [
                'missing_config' => $missingConfig
            ]);

            // Force rebuild cache
            $this->configService->rebuildConfigCache();

            // Thử load lại config
            $this->accessToken = $this->configService->get('api.access_token');
            $this->appId = $this->configService->get('api.app_id');
            $this->appSecret = $this->configService->get('api.secret');

            // Kiểm tra lại sau khi rebuild
            $missingConfig = [];
            if (empty($this->accessToken)) {
                $missingConfig[] = 'Access Token';
            }
            if (empty($this->appId)) {
                $missingConfig[] = 'App ID';
            }
            if (empty($this->appSecret)) {
                $missingConfig[] = 'App Secret';
            }

            // Nếu vẫn còn thiếu config sau khi rebuild
            if (!empty($missingConfig)) {
                Log::error('TikTok API configuration incomplete', [
                    'missing_config' => $missingConfig,
                    'service' => 'TikTokApiService'
                ]);

                // Store error for later retrieval by calling code
                $this->lastError = $this->createConfigurationErrorResponse($missingConfig);
            } else {
                Log::info('TikTok API configuration recovered after cache rebuild');
            }
        }

        Log::info('TikTok API Service initialized with production credentials');
    }

    /**
     * Create detailed configuration error response with user-friendly instructions
     */
    private function createConfigurationErrorResponse(array $missingConfig): array
    {
        $missing = implode(', ', $missingConfig);

        // Create detailed instructions based on what's missing
        $instructions = $this->getConfigurationInstructions($missingConfig);

        $message = "❌ TikTok API không thể hoạt động do thiếu cấu hình quan trọng.\n\n";
        $message .= "🔧 Thiếu: {$missing}\n\n";
        $message .= "📋 Hướng dẫn khắc phục:\n{$instructions}";

        return ErrorHandler::createErrorResponse(
            $message,
            ErrorHandler::CONFIGURATION_ERROR,
            [
                'missing_config' => $missingConfig,
                'instructions' => $instructions,
                'admin_panel_url' => '/admin/tik-tok-settings',
                'debug_command' => 'php artisan tiktok:debug-config'
            ]
        );
    }

    /**
     * Get specific instructions based on missing configuration
     */
    private function getConfigurationInstructions(array $missingConfig): string
    {
        $instructions = [];

        if (in_array('Access Token', $missingConfig, true)) {
            $instructions[] = "🔑 Access Token: Lấy từ TikTok Business API Dashboard";
            $instructions[] = "   - Truy cập: https://business-api.tiktok.com/";
            $instructions[] = "   - Tạo app và lấy access token";
        }

        if (in_array('App ID', $missingConfig, true)) {
            $instructions[] = "🆔 App ID: ID của ứng dụng TikTok Business";
            $instructions[] = "   - Tìm trong TikTok Business API Dashboard";
        }

        if (in_array('App Secret', $missingConfig, true)) {
            $instructions[] = "🔐 App Secret: Khóa bí mật của ứng dụng";
            $instructions[] = "   - Lấy từ TikTok Business API Dashboard";
        }

        $instructions[] = "";
        $instructions[] = "⚙️ Cách cấu hình:";
        $instructions[] = "1. Truy cập Admin Panel: /admin/tik-tok-settings";
        $instructions[] = "2. Hoặc chạy: php artisan tiktok:setup-credentials";
        $instructions[] = "3. Kiểm tra: php artisan tiktok:debug-config";

        return implode("\n", $instructions);
    }

    /**
     * Set advertiser ID for API calls
     */
    public function setAdvertiserId(string $advertiserId): self
    {
        $this->advertiserId = $advertiserId;
        return $this;
    }

    /**
     * Get current advertiser ID
     */
    public function getAdvertiserId(): ?string
    {
        return $this->advertiserId;
    }

    /**
     * Make authenticated API request to TikTok
     */
    protected function makeRequest(string $method, string $endpoint, array $data = []): array
    {
        $url = $this->baseUrl . '/open_api/v1.3' . $endpoint;

        Log::info('TikTok API Request', [
            'method' => $method,
            'url' => $url,
            'data' => $data
        ]);

        try {
            $response = Http::withHeaders([
                'Access-Token' => $this->accessToken,
                'Content-Type' => 'application/json',
            ])->timeout(30);

            // Add rate limiting
            sleep($this->rateLimitDelay);

            $result = match (strtoupper($method)) {
                'GET' => $response->get($url, $data),
                'POST' => $response->post($url, $data),
                'PUT' => $response->put($url, $data),
                'DELETE' => $response->delete($url, $data),
                default => null
            };

            if ($result === null) {
                return ErrorHandler::createErrorResponse(
                    "Unsupported HTTP method: {$method}",
                    ErrorHandler::VALIDATION_ERROR,
                    ['method' => $method, 'endpoint' => $endpoint]
                );
            }

            if ($result->failed()) {
                return ErrorHandler::handleHttpError(
                    $result,
                    'TikTok API',
                    ['method' => $method, 'endpoint' => $endpoint, 'data' => $data]
                );
            }

            $responseData = $result->json();

            Log::info('TikTok API Response', [
                'status' => $result->status(),
                'data' => $responseData
            ]);

            // Check TikTok API response code
            if (isset($responseData['code']) && $responseData['code'] !== 0) {
                return $this->handleTikTokApiError($responseData, $method, $endpoint);
            }

            return ErrorHandler::createSuccessResponse([
                'data' => $responseData['data'] ?? $responseData
            ]);

        } catch (Exception $e) {
            return ErrorHandler::handleNetworkError(
                $e,
                'TikTok API',
                ['method' => $method, 'endpoint' => $endpoint]
            );
        }
    }

    /**
     * Handle TikTok API errors with user-friendly messages
     */
    private function handleTikTokApiError(array $responseData, string $method, string $endpoint): array
    {
        $apiCode = $responseData['code'] ?? 0;
        $apiMessage = $responseData['message'] ?? 'Unknown error';

        // Create user-friendly error message based on common error codes
        $userMessage = $this->getUserFriendlyErrorMessage($apiCode, $apiMessage);

        return ErrorHandler::createErrorResponse(
            $userMessage,
            ErrorHandler::API_ERROR,
            [
                'api_code' => $apiCode,
                'api_message' => $apiMessage,
                'method' => $method,
                'endpoint' => $endpoint,
                'original_response' => $responseData
            ]
        );
    }

    /**
     * Convert TikTok API error codes to user-friendly messages
     */
    private function getUserFriendlyErrorMessage(int $code, string $message): string
    {
        $userFriendlyMessages = [
            // Authentication errors
            40001 => "❌ Access Token không hợp lệ hoặc đã hết hạn.\n\n🔧 Khắc phục:\n1. Kiểm tra Access Token trong Admin Panel: /admin/tik-tok-settings\n2. Lấy Access Token mới từ TikTok Business API Dashboard\n3. Chạy: php artisan tiktok:debug-config để kiểm tra",

            40002 => "❌ Access Token bị thiếu trong request.\n\n🔧 Khắc phục:\n1. Cấu hình Access Token trong Admin Panel: /admin/tik-tok-settings\n2. Chạy: php artisan tiktok:setup-credentials",

            40003 => "❌ Access Token không có quyền truy cập tài nguyên này.\n\n🔧 Khắc phục:\n1. Kiểm tra quyền của Access Token\n2. Đảm bảo token có quyền truy cập Advertiser Account",

            // App configuration errors
            40100 => "❌ App ID không hợp lệ.\n\n🔧 Khắc phục:\n1. Kiểm tra App ID trong Admin Panel: /admin/tik-tok-settings\n2. Lấy App ID đúng từ TikTok Business API Dashboard",

            40101 => "❌ App Secret không hợp lệ.\n\n🔧 Khắc phục:\n1. Kiểm tra App Secret trong Admin Panel: /admin/tik-tok-settings\n2. Lấy App Secret đúng từ TikTok Business API Dashboard",

            // Rate limiting
            40301 => "⚠️ Đã vượt quá giới hạn API calls.\n\n🔧 Khắc phục:\n1. Chờ một lúc rồi thử lại\n2. Giảm tần suất sync trong cấu hình\n3. Liên hệ TikTok để tăng rate limit",

            // Permission errors
            40401 => "❌ Không có quyền truy cập Advertiser Account.\n\n🔧 Khắc phục:\n1. Kiểm tra Advertiser ID\n2. Đảm bảo Access Token có quyền truy cập account này\n3. Liên hệ admin TikTok Business để cấp quyền",

            // Data errors
            40601 => "❌ Dữ liệu request không hợp lệ.\n\n🔧 Khắc phục:\n1. Kiểm tra format dữ liệu gửi lên\n2. Xem log chi tiết để biết field nào bị lỗi",

            // Server errors
            50001 => "⚠️ Lỗi server TikTok API tạm thời.\n\n🔧 Khắc phục:\n1. Thử lại sau vài phút\n2. Nếu lỗi kéo dài, liên hệ TikTok support",
        ];

        // Return specific message if available, otherwise generic message
        return $userFriendlyMessages[$code] ?? "❌ TikTok API Error (Code: {$code})\n\n📝 Chi tiết: {$message}\n\n🔧 Khắc phục:\n1. Kiểm tra cấu hình TikTok: /admin/tik-tok-settings\n2. Chạy: php artisan tiktok:debug-config\n3. Xem log chi tiết để biết thêm thông tin";
    }

    /**
     * Get GMV Max Campaign details
     *
     * @param string $campaignId
     * @return array
     */
    public function getCampaignDetails(string $campaignId): array
    {
        if (empty($this->advertiserId)) {
            return ErrorHandler::createErrorResponse(
                'Advertiser ID is required for getting campaign details',
                ErrorHandler::CONFIGURATION_ERROR
            );
        }

        $endpoint = '/campaign/gmv_max/info/';
        $params = [
            'advertiser_id' => $this->advertiserId,
            'campaign_id' => $campaignId
        ];

        Log::info('Getting GMV Max Campaign details', [
            'advertiser_id' => $this->advertiserId,
            'campaign_id' => $campaignId,
            'endpoint' => $endpoint
        ]);

        return $this->makeRequest('GET', $endpoint, $params);
    }

    /**
     * Get cached data or make API request
     */
    protected function getCachedOrFetch(string $cacheKey, callable $fetchCallback, int $ttl = 3600): array
    {
        return Cache::remember($cacheKey, $ttl, $fetchCallback);
    }

    // ==================== CAMPAIGN MANAGEMENT APIs ====================

    /**
     * Retrieve GMV Max Campaigns
     *
     * @param array $options Options for filtering campaigns
     * @return array Campaign data with pagination info
     * @throws Exception When API request fails or validation errors occur
     */
    public function getCampaigns(array $options = []): array
    {
        // Validate required parameters
        if (!$this->advertiserId) {
            throw new RuntimeException('Advertiser ID is required for campaign retrieval');
        }

        // Build filtering object according to TikTok API spec
        $filtering = [];

        // GMV Max promotion types (required)
        $filtering['gmv_max_promotion_types'] = $options['gmv_max_promotion_types'] ?? ['PRODUCT_GMV_MAX'];

        // Validate promotion types
        $validPromotionTypes = ['PRODUCT_GMV_MAX', 'LIVE_GMV_MAX'];
        foreach ($filtering['gmv_max_promotion_types'] as $type) {
            if (!in_array($type, $validPromotionTypes, true)) {
                throw new RuntimeException("Invalid promotion type: {$type}. Valid types: " . implode(', ', $validPromotionTypes));
            }
        }

        // Optional filters with validation
        if (isset($options['store_ids'])) {
            if (!is_array($options['store_ids'])) {
                throw new RuntimeException('store_ids must be an array');
            }
            $filtering['store_ids'] = array_slice($options['store_ids'], 0, 10); // Max 10
        }

        if (isset($options['campaign_ids'])) {
            if (!is_array($options['campaign_ids'])) {
                throw new RuntimeException('campaign_ids must be an array');
            }
            $filtering['campaign_ids'] = array_slice($options['campaign_ids'], 0, 100); // Max 100
        }

        if (isset($options['campaign_name'])) {
            $filtering['campaign_name'] = (string)$options['campaign_name'];
        }

        if (isset($options['primary_status'])) {
            $validStatuses = ['STATUS_DELIVERY_OK', 'STATUS_DISABLE', 'STATUS_DELETE'];
            if (!in_array($options['primary_status'], $validStatuses, true)) {
                throw new RuntimeException("Invalid primary_status: {$options['primary_status']}. Valid statuses: " . implode(', ', $validStatuses));
            }
            $filtering['primary_status'] = $options['primary_status'];
        }

        // Time filters with validation
        if (isset($options['creation_filter_start_time'])) {
            if (!$this->isValidDateTimeFormat($options['creation_filter_start_time'])) {
                throw new RuntimeException('creation_filter_start_time must be in format YYYY-MM-DD HH:MM:SS');
            }
            $filtering['creation_filter_start_time'] = $options['creation_filter_start_time'];
        }

        if (isset($options['creation_filter_end_time'])) {
            if (!$this->isValidDateTimeFormat($options['creation_filter_end_time'])) {
                throw new RuntimeException('creation_filter_end_time must be in format YYYY-MM-DD HH:MM:SS');
            }
            $filtering['creation_filter_end_time'] = $options['creation_filter_end_time'];
        }

        // Build request parameters
        $params = [
            'advertiser_id' => $this->advertiserId,
            'filtering' => json_encode($filtering, JSON_THROW_ON_ERROR),
            'page' => max(1, (int)($options['page'] ?? 1)),
            'page_size' => min(max(1, (int)($options['page_size'] ?? 10)), 100), // Range: 1-100
        ];

        // Optional fields parameter with optimization
        if (isset($options['fields'])) {
            if (!is_array($options['fields'])) {
                throw new RuntimeException('fields must be an array');
            }
            $params['fields'] = $options['fields'];
        } else {
            // Default optimized fields for better performance
            $params['fields'] = $this->getDefaultCampaignFields();
        }

        try {
            return $this->getCachedOrFetch(
                "tiktok_campaigns_{$this->advertiserId}_" . md5(serialize($params)),
                fn() => $this->makeRequest('GET', '/gmv_max/campaign/get/', $params),
                1800 // 30 minutes cache
            );
        } catch (Exception $e) {
            Log::error('Failed to retrieve GMV Max campaigns', [
                'advertiser_id' => $this->advertiserId,
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Get Product GMV Max Campaigns specifically
     * @throws Exception
     */
    public function getProductGmvMaxCampaigns(array $options = []): array
    {
        return $this->getCampaigns(array_merge($options, [
            'gmv_max_promotion_types' => ['PRODUCT_GMV_MAX']
        ]));
    }

    /**
     * Get Live GMV Max Campaigns specifically
     * @throws Exception
     */
    public function getLiveGmvMaxCampaigns(array $options = []): array
    {
        return $this->getCampaigns(array_merge($options, [
            'gmv_max_promotion_types' => ['LIVE_GMV_MAX']
        ]));
    }

    /**
     * Get campaigns with ROI protection status filter
     * @throws Exception
     */
    public function getCampaignsByRoiProtection(string $roiStatus, array $options = []): array
    {
        $validStatuses = ['IN_EFFECT', 'NOT_ELIGIBLE'];
        if (!in_array($roiStatus, $validStatuses)) {
            throw new RuntimeException("Invalid ROI protection status: {$roiStatus}. Valid statuses: " . implode(', ', $validStatuses));
        }

        // Note: ROI protection status is returned in response but not available as filter in current API
        // This method filters results after retrieval
        $campaigns = $this->getCampaigns($options);

        if (isset($campaigns['list'])) {
            $campaigns['list'] = array_filter($campaigns['list'], static function ($campaign) use ($roiStatus) {
                return isset($campaign['roi_protection_compensation_status']) &&
                    $campaign['roi_protection_compensation_status'] === $roiStatus;
            });

            // Update pagination info
            $campaigns['page_info']['total_number'] = count($campaigns['list']);
            $campaigns['page_info']['total_page'] = ceil($campaigns['page_info']['total_number'] / $campaigns['page_info']['page_size']);
        }

        return $campaigns;
    }

    /**
     * Get campaigns with minimal fields for performance
     * @throws Exception
     */
    public function getCampaignsMinimal(array $options = []): array
    {
        return $this->getCampaigns(array_merge($options, [
            'fields' => ['campaign_id', 'campaign_name', 'operation_status', 'primary_status', 'advertiser_id']
        ]));
    }

    /**
     * Get campaigns with full details
     * @throws Exception
     */
    public function getCampaignsFull(array $options = []): array
    {
        return $this->getCampaigns(array_merge($options, [
            'fields' => $this->getAllCampaignFields()
        ]));
    }

    /**
     * Get campaigns by status with improved mapping
     * @throws Exception
     */
    public function getCampaignsByStatus(string $status, array $options = []): array
    {
        $statusMap = [
            'active' => 'STATUS_DELIVERY_OK',
            'inactive' => 'STATUS_DISABLE',
            'disabled' => 'STATUS_DISABLE',
            'deleted' => 'STATUS_DELETE'
        ];

        $mappedStatus = $statusMap[$status] ?? $status;

        // Validate status
        $validStatuses = ['STATUS_DELIVERY_OK', 'STATUS_DISABLE', 'STATUS_DELETE'];
        if (!in_array($mappedStatus, $validStatuses)) {
            throw new RuntimeException("Invalid status: {$status}. Valid statuses: " . implode(', ', array_keys($statusMap)));
        }

        return $this->getCampaigns(array_merge($options, [
            'primary_status' => $mappedStatus
        ]));
    }

    /**
     * Get campaign details
     */
    public function getCampaignInfo(string $campaignId): array
    {
        return $this->getCachedOrFetch(
            "tiktok_campaign_info_{$campaignId}",
            fn() => $this->makeRequest('GET', '/campaign/gmv_max/info/', [
                'advertiser_id' => $this->advertiserId,
                'campaign_id' => $campaignId
            ]),
            900 // 15 minutes cache
        );
    }

    /**
     * Create new campaign
     */
    public function createCampaign(array $campaignData): array
    {
        $data = array_merge([
            'advertiser_id' => $this->advertiserId,
        ], $campaignData);

        $result = $this->makeRequest('POST', '/campaign/gmv_max/create/', $data);

        // Clear campaigns cache
        Cache::forget("tiktok_campaigns_{$this->advertiserId}_*");

        return $result;
    }

    /**
     * Update existing campaign
     */
    public function updateCampaign(string $campaignId, array $updateData): array
    {
        $data = array_merge([
            'advertiser_id' => $this->advertiserId,
            'campaign_id' => $campaignId,
        ], $updateData);

        $result = $this->makeRequest('POST', '/campaign/gmv_max/update/', $data);

        // Clear related caches
        Cache::forget("tiktok_campaign_info_{$campaignId}");
        Cache::forget("tiktok_campaigns_{$this->advertiserId}_*");

        return $result;
    }

    /**
     * Get recommended ROI targets and budgets
     */
    public function getRecommendedBid(string $shopId, array $options = []): array
    {
        $params = array_merge([
            'advertiser_id' => $this->advertiserId,
            'shop_id' => $shopId,
        ], $options);

        return $this->makeRequest('GET', '/gmv_max/bid/recommend/', $params);
    }

    // ==================== SESSION MANAGEMENT APIs ====================

    /**
     * Create delivery session
     */
    public function createSession(string $campaignId, array $sessionData): array
    {
        $data = array_merge([
            'advertiser_id' => $this->advertiserId,
            'campaign_id' => $campaignId,
        ], $sessionData);

        return $this->makeRequest('POST', '/campaign/gmv_max/session/create/', $data);
    }

    /**
     * Update session
     */
    public function updateSession(string $sessionId, array $updateData): array
    {
        $data = array_merge([
            'advertiser_id' => $this->advertiserId,
            'session_id' => $sessionId,
        ], $updateData);

        return $this->makeRequest('POST', '/campaign/gmv_max/session/update/', $data);
    }

    /**
     * List sessions within campaigns
     */
    public function listSessions(string $campaignId): array
    {
        return $this->getCachedOrFetch(
            "tiktok_sessions_{$campaignId}",
            fn() => $this->makeRequest('GET', '/campaign/gmv_max/session/list/', [
                'advertiser_id' => $this->advertiserId,
                'campaign_id' => $campaignId
            ]),
            900 // 15 minutes cache
        );
    }

    /**
     * Get session details
     */
    public function getSessionDetails(string $sessionId): array
    {
        return $this->getCachedOrFetch(
            "tiktok_session_details_{$sessionId}",
            fn() => $this->makeRequest('GET', '/campaign/gmv_max/session/get/', [
                'advertiser_id' => $this->advertiserId,
                'session_id' => $sessionId
            ]),
            900 // 15 minutes cache
        );
    }

    /**
     * Delete session
     */
    public function deleteSession(string $sessionId): array
    {
        $result = $this->makeRequest('POST', '/campaign/gmv_max/session/delete/', [
            'advertiser_id' => $this->advertiserId,
            'session_id' => $sessionId
        ]);

        // Clear session cache
        Cache::forget("tiktok_session_details_{$sessionId}");

        return $result;
    }

    // ==================== STORE & PRODUCT MANAGEMENT APIs ====================

    /**
     * Get TikTok Shops for GMV Max
     */
    public function getStores(): array
    {
        return $this->getCachedOrFetch(
            "tiktok_stores_{$this->advertiserId}",
            fn() => $this->makeRequest('GET', '/gmv_max/store/list/', [
                'advertiser_id' => $this->advertiserId
            ]) // 1 hour cache
        );
    }

    /**
     * Check shop availability for ads
     */
    public function checkShopAdUsage(string $shopId): array
    {
        return $this->makeRequest('GET', '/gmv_max/store/shop_ad_usage_check/', [
            'advertiser_id' => $this->advertiserId,
            'shop_id' => $shopId
        ]);
    }

    /**
     * Get products within a TikTok Shop
     * Endpoint: /v1.3/store/product/get/
     *
     * Note: This endpoint requires special store management permissions.
     * If permission error occurs, will fallback to alternative methods.
     */
    public function getStoreProducts(string $bcId, string $storeId, array $options = []): array
    {
        $params = [
            'bc_id' => $bcId,
            'store_id' => $storeId,
        ];

        // Add filtering options
        if (isset($options['filtering'])) {
            $params['filtering'] = $options['filtering'];
        }

        // Add advertiser_id if ad_creation_eligible is specified
        if (isset($options['filtering']['ad_creation_eligible']) && $this->advertiserId) {
            $params['advertiser_id'] = $this->advertiserId;
        }

        // Add sorting options
        if (isset($options['sort_field'])) {
            $params['sort_field'] = $options['sort_field'];
        }
        if (isset($options['sort_type'])) {
            $params['sort_type'] = $options['sort_type'];
        }

        // Add pagination
        $params['page'] = $options['page'] ?? 1;
        $params['page_size'] = $options['page_size'] ?? 100;

        // Create cache key based on all parameters
        $cacheKey = "tiktok_store_products_{$storeId}_" . md5(serialize($params));

        $result = $this->getCachedOrFetch(
            $cacheKey,
            fn() => $this->makeRequest('GET', '/store/product/get/', $params),
            1800 // 30 minutes cache
        );

        // Check for permission errors and try fallback
        if (!ErrorHandler::isSuccess($result) && $this->isPermissionError($result)) {
            Log::warning('Store products API permission denied, trying fallback method', [
                'store_id' => $storeId,
                'error' => $result['message'] ?? 'Unknown error'
            ]);

            return $this->getStoreProductsFallback($bcId, $storeId, $options);
        }

        return $result;
    }

    /**
     * Fallback method to get store products using alternative endpoints
     */
    protected function getStoreProductsFallback(string $bcId, string $storeId, array $options = []): array
    {
        try {
            // Try to get products from occupied ads endpoint
            $occupiedResult = $this->getOccupiedCustomShopAds();

            if (!ErrorHandler::isSuccess($occupiedResult)) {
                return ErrorHandler::createErrorResponse(
                    'Không thể truy cập dữ liệu sản phẩm. Vui lòng kiểm tra quyền truy cập TikTok API.',
                    ErrorHandler::PERMISSION_ERROR,
                    [
                        'required_permissions' => [
                            'store_management',
                            'product_management'
                        ],
                        'suggested_action' => 'Contact TikTok support to grant store product access permissions',
                        'alternative' => 'Using limited data from occupied ads endpoint'
                    ]
                );
            }

            // Extract product data from occupied ads
            $occupiedData = $occupiedResult['data'] ?? [];
            $products = [];

            // Filter by store_id if available in occupied data
            foreach ($occupiedData as $item) {
                if (isset($item['store_id']) && $item['store_id'] === $storeId) {
                    $products[] = $this->mapOccupiedAdToProduct($item);
                }
            }

            return ErrorHandler::createSuccessResponse([
                'store_products' => $products,
                'page_info' => [
                    'page' => 1,
                    'page_size' => count($products),
                    'total_number' => count($products),
                    'total_page' => 1
                ],
                'data_source' => 'fallback_occupied_ads',
                'limitations' => [
                    'Only shows products currently in use',
                    'Limited product information available',
                    'No historical sales data'
                ]
            ]);

        } catch (Exception $e) {
            return ErrorHandler::createErrorResponse(
                'Fallback method failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Map occupied ad data to product format
     */
    protected function mapOccupiedAdToProduct(array $occupiedAd): array
    {
        return [
            'item_group_id' => $occupiedAd['occupied_asset_id'] ?? null,
            'store_id' => $occupiedAd['store_id'] ?? null,
            'title' => $occupiedAd['asset_name'] ?? 'Unknown Product',
            'status' => 'AVAILABLE',
            'gmv_max_ads_status' => 'OCCUPIED',
            'is_running_custom_shop_ads' => true,
            'min_price' => '0',
            'max_price' => '0',
            'currency' => 'USD',
            'historical_sales' => 0,
            'category' => null,
            'product_image_url' => null,
            'catalog_id' => null,
            '_data_source' => 'occupied_ads_fallback'
        ];
    }

    /**
     * Check if error is a permission error
     */
    protected function isPermissionError(array $result): bool
    {
        $errorCode = $result['context']['api_code'] ?? null;
        $errorMessage = $result['message'] ?? '';

        return $errorCode === 40001 ||
            str_contains($errorMessage, 'permission') ||
            str_contains($errorMessage, 'grant');
    }

    /**
     * Get products available for GMV Max campaigns
     */
    public function getGmvMaxEligibleProducts(string $bcId, string $storeId, array $options = []): array
    {
        $filtering = array_merge(
            $options['filtering'] ?? [],
            ['ad_creation_eligible' => 'GMV_MAX']
        );

        return $this->getStoreProducts($bcId, $storeId, array_merge($options, [
            'filtering' => $filtering
        ]));
    }

    /**
     * Get products available for Shopping Ads
     */
    public function getShoppingAdsEligibleProducts(string $bcId, string $storeId, array $options = []): array
    {
        $filtering = array_merge(
            $options['filtering'] ?? [],
            ['ad_creation_eligible' => 'CUSTOM_SHOP_ADS']
        );

        return $this->getStoreProducts($bcId, $storeId, array_merge($options, [
            'filtering' => $filtering
        ]));
    }

    /**
     * Check product/identity occupancy
     */
    public function getOccupiedCustomShopAds(): array
    {
        return $this->getCachedOrFetch(
            "tiktok_occupied_ads_{$this->advertiserId}",
            fn() => $this->makeRequest('GET', '/gmv_max/occupied_custom_shop_ads/list/', [
                'advertiser_id' => $this->advertiserId
            ]),
            1800 // 30 minutes cache
        );
    }

    // ==================== IDENTITY & VIDEO MANAGEMENT APIs ====================

    /**
     * Get identities for campaigns
     */
    public function getIdentities(): array
    {
        return $this->getCachedOrFetch(
            "tiktok_identities_{$this->advertiserId}",
            fn() => $this->makeRequest('GET', '/gmv_max/identity/get/', [
                'advertiser_id' => $this->advertiserId
            ]) // 1 hour cache
        );
    }

    /**
     * Get posts for Product GMV Max campaigns
     *
     * @param array $options Configuration options for video retrieval
     * @return array Video data with pagination info
     * @throws RuntimeException When validation fails or required parameters are missing
     */
    public function getVideos(array $options = []): array
    {
        // Validate required parameters
        if (!$this->advertiserId) {
            throw new RuntimeException('Advertiser ID is required for video retrieval');
        }

        if (empty($options['store_id'])) {
            throw new RuntimeException('store_id is required');
        }

        if (empty($options['store_authorized_bc_id'])) {
            throw new RuntimeException('store_authorized_bc_id is required');
        }

        // Build base parameters
        $params = [
            'advertiser_id' => $this->advertiserId,
            'store_id' => $options['store_id'],
            'store_authorized_bc_id' => $options['store_authorized_bc_id'],
        ];

        // Add optional SPU ID list with validation
        if (isset($options['spu_id_list'])) {
            if (!is_array($options['spu_id_list'])) {
                throw new RuntimeException('spu_id_list must be an array');
            }

            $customPostsEligible = $options['custom_posts_eligible'] ?? false;
            $maxSize = $customPostsEligible ? 1 : 50;

            if (count($options['spu_id_list']) > $maxSize) {
                throw new RuntimeException("spu_id_list cannot exceed {$maxSize} items when custom_posts_eligible is " . ($customPostsEligible ? 'true' : 'false'));
            }

            $params['spu_id_list'] = $options['spu_id_list'];
        }

        // Add custom posts eligibility
        if (isset($options['custom_posts_eligible'])) {
            if (!is_bool($options['custom_posts_eligible'])) {
                throw new RuntimeException('custom_posts_eligible must be a boolean');
            }

            // If custom_posts_eligible is true, spu_id_list must contain exactly 1 item
            if ($options['custom_posts_eligible'] &&
                (!isset($options['spu_id_list']) || count($options['spu_id_list']) !== 1)) {
                throw new RuntimeException('When custom_posts_eligible is true, spu_id_list must contain exactly 1 product');
            }

            $params['custom_posts_eligible'] = $options['custom_posts_eligible'];
        }

        // Add sorting options (only valid when custom_posts_eligible is false or not provided)
        if (!($options['custom_posts_eligible'] ?? false)) {
            if (isset($options['sort_field'])) {
                $validSortFields = ['GMV', 'POST_TIME', 'VIDEO_VIEWS', 'VIDEO_LIKES', 'CLICK_THROUGH_RATE', 'PRODUCT_CLICKS'];
                if (!in_array($options['sort_field'], $validSortFields)) {
                    throw new RuntimeException('Invalid sort_field. Must be one of: ' . implode(', ', $validSortFields));
                }
                $params['sort_field'] = $options['sort_field'];
            }
        }

        if (isset($options['sort_type'])) {
            $validSortTypes = ['ASC', 'DESC'];
            if (!in_array($options['sort_type'], $validSortTypes)) {
                throw new RuntimeException('Invalid sort_type. Must be ASC or DESC');
            }
            $params['sort_type'] = $options['sort_type'];
        }

        // Add search keyword
        if (isset($options['keyword'])) {
            if (!is_string($options['keyword']) || empty(trim($options['keyword']))) {
                throw new RuntimeException('keyword must be a non-empty string');
            }
            $params['keyword'] = trim($options['keyword']);
        }

        // Add auth code video option
        if (isset($options['need_auth_code_video'])) {
            if (!is_bool($options['need_auth_code_video'])) {
                throw new RuntimeException('need_auth_code_video must be a boolean');
            }
            $params['need_auth_code_video'] = $options['need_auth_code_video'];
        }

        // Add identity list with validation
        if (isset($options['identity_list'])) {
            if (!is_array($options['identity_list'])) {
                throw new RuntimeException('identity_list must be an array');
            }

            if (count($options['identity_list']) > 20) {
                throw new RuntimeException('identity_list cannot exceed 20 items');
            }

            // Validate each identity in the list
            foreach ($options['identity_list'] as $index => $identity) {
                $this->validateIdentity($identity, $index);
            }

            $params['identity_list'] = $options['identity_list'];
        }

        // Add pagination
        $params['page'] = max(1, (int)($options['page'] ?? 1));
        $params['page_size'] = min(max(1, (int)($options['page_size'] ?? 10)), 50);

        try {
            return $this->getCachedOrFetch(
                "tiktok_videos_{$this->advertiserId}_" . md5(serialize($params)),
                fn() => $this->makeRequest('GET', '/gmv_max/video/get/', $params),
                1800 // 30 minutes cache
            );
        } catch (Exception $e) {
            Log::error('Failed to retrieve GMV Max videos', [
                'advertiser_id' => $this->advertiserId,
                'options' => $options,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Validate identity structure for video retrieval
     *
     * @param array $identity Identity data to validate
     * @param int $index Index in the identity list for error reporting
     * @throws RuntimeException When identity validation fails
     */
    private function validateIdentity(array $identity, int $index): void
    {
        if (empty($identity['identity_id'])) {
            throw new RuntimeException("identity_list[{$index}]: identity_id is required");
        }

        if (empty($identity['identity_type'])) {
            throw new RuntimeException("identity_list[{$index}]: identity_type is required");
        }

        $validIdentityTypes = ['TT_USER', 'BC_AUTH_TT', 'TTS_TT'];
        if (!in_array($identity['identity_type'], $validIdentityTypes)) {
            throw new RuntimeException("identity_list[{$index}]: Invalid identity_type. Must be one of: " . implode(', ', $validIdentityTypes));
        }

        // Validate conditional fields based on identity type
        switch ($identity['identity_type']) {
            case 'BC_AUTH_TT':
                if (empty($identity['identity_authorized_bc_id'])) {
                    throw new RuntimeException("identity_list[{$index}]: identity_authorized_bc_id is required for BC_AUTH_TT identity type");
                }
                // identity_authorized_shop_id is optional for BC_AUTH_TT
                break;

            case 'TTS_TT':
                if (empty($identity['store_id'])) {
                    throw new RuntimeException("identity_list[{$index}]: store_id is required for TTS_TT identity type");
                }
                break;

            case 'TT_USER':
                // No additional required fields for TT_USER
                break;
        }
    }

    /**
     * Get custom video details
     */
    public function getCustomAnchorVideos(): array
    {
        return $this->getCachedOrFetch(
            "tiktok_custom_videos_{$this->advertiserId}",
            fn() => $this->makeRequest('GET', '/gmv_max/custom_anchor_video_list/get/', [
                'advertiser_id' => $this->advertiserId
            ]),
            1800 // 30 minutes cache
        );
    }

    // ==================== AUTHORIZATION MANAGEMENT APIs ====================

    /**
     * Get shop authorization status
     * Updated to match v1.3 API specification
     */
    public function getExclusiveAuthorization(string $storeId, string $storeAuthorizedBcId, ?string $advertiserId = null): array
    {
        $advertiserId = $advertiserId ?? $this->advertiserId;

        if (!$advertiserId) {
            return ErrorHandler::createErrorResponse(
                'Advertiser ID is required for exclusive authorization check',
                ErrorHandler::VALIDATION_ERROR
            );
        }

        $params = [
            'store_id' => $storeId,
            'store_authorized_bc_id' => $storeAuthorizedBcId,
            'advertiser_id' => $advertiserId
        ];

        return $this->getCachedOrFetch(
            "tiktok_auth_{$storeId}_{$advertiserId}",
            fn() => $this->makeRequest('GET', '/gmv_max/exclusive_authorization/get/', $params),
            900 // 15 minutes cache
        );
    }

    /**
     * Get authorization status for multiple shops
     */
    public function getBatchExclusiveAuthorizations(array $shops, ?string $advertiserId = null): array
    {
        $advertiserId = $advertiserId ?? $this->advertiserId;
        $results = [];

        foreach ($shops as $shop) {
            $storeId = is_array($shop) ? $shop['store_id'] : $shop->shop_id;
            $bcId = is_array($shop) ? $shop['store_authorized_bc_id'] : $shop->store_authorized_bc_id;

            if ($storeId && $bcId) {
                try {
                    $result = $this->getExclusiveAuthorization($storeId, $bcId, $advertiserId);
                    $results[$storeId] = $result;
                } catch (Exception $e) {
                    $results[$storeId] = ErrorHandler::createErrorResponse(
                        'Failed to get authorization for store: ' . $e->getMessage()
                    );
                }
            }
        }

        return ErrorHandler::createSuccessResponse(['authorizations' => $results]);
    }

    /**
     * Grant exclusive authorization for a TikTok Shop
     * Updated to match TikTok API v1.3 specification
     */
    public function createExclusiveAuthorization(
        string  $storeId,
        string  $storeAuthorizedBcId,
        ?string $advertiserId = null
    ): array
    {
        $advertiserId = $advertiserId ?? $this->advertiserId;

        // Validate required parameters
        if (!$advertiserId) {
            return ErrorHandler::createErrorResponse(
                'Advertiser ID is required for granting exclusive authorization',
                ErrorHandler::VALIDATION_ERROR
            );
        }

        if (!$storeId) {
            return ErrorHandler::createErrorResponse(
                'Store ID is required for granting exclusive authorization',
                ErrorHandler::VALIDATION_ERROR
            );
        }

        if (!$storeAuthorizedBcId) {
            return ErrorHandler::createErrorResponse(
                'Store authorized Business Center ID is required for granting exclusive authorization',
                ErrorHandler::VALIDATION_ERROR
            );
        }

        $data = [
            'store_id' => $storeId,
            'store_authorized_bc_id' => $storeAuthorizedBcId,
            'advertiser_id' => $advertiserId
        ];

        Log::info('Granting exclusive authorization', [
            'store_id' => $storeId,
            'store_authorized_bc_id' => $storeAuthorizedBcId,
            'advertiser_id' => $advertiserId
        ]);

        $result = $this->makeRequest('POST', '/gmv_max/exclusive_authorization/create/', $data);

        // Clear authorization cache for this store and advertiser
        Cache::forget("tiktok_auth_{$storeId}_{$advertiserId}");
        Cache::forget("tiktok_auth_{$storeId}");

        if (ErrorHandler::isSuccess($result)) {
            Log::info('Exclusive authorization granted successfully', [
                'store_id' => $storeId,
                'advertiser_id' => $advertiserId
            ]);
        } else {
            Log::error('Failed to grant exclusive authorization', [
                'store_id' => $storeId,
                'advertiser_id' => $advertiserId,
                'error' => ErrorHandler::getErrorMessage($result)
            ]);
        }

        return $result;
    }

    /**
     * Batch grant exclusive authorization for multiple shops
     */
    public function batchCreateExclusiveAuthorizations(array $shops, ?string $advertiserId = null): array
    {
        $advertiserId = $advertiserId ?? $this->advertiserId;
        $results = [];

        foreach ($shops as $shop) {
            $storeId = is_array($shop) ? $shop['store_id'] : $shop->shop_id;
            $bcId = is_array($shop) ? $shop['store_authorized_bc_id'] : $shop->store_authorized_bc_id;

            if ($storeId && $bcId) {
                try {
                    $result = $this->createExclusiveAuthorization($storeId, $bcId, $advertiserId);
                    $results[$storeId] = $result;
                } catch (Exception $e) {
                    $results[$storeId] = ErrorHandler::createErrorResponse(
                        'Failed to grant authorization for store: ' . $e->getMessage()
                    );
                }
            } else {
                $results[$storeId] = ErrorHandler::createErrorResponse(
                    'Missing required store information (store_id or store_authorized_bc_id)',
                    ErrorHandler::VALIDATION_ERROR
                );
            }
        }

        return ErrorHandler::createSuccessResponse(['authorizations' => $results]);
    }

    // ==================== REPORTING APIs ====================

    /**
     * Run GMV Max campaign reports with full API specification support
     * @throws Exception
     */
    public function getCampaignReports(array $reportParams): array
    {
        // Validate required parameters
        $this->validateReportParams($reportParams);

        $params = array_merge([
            'advertiser_id' => $this->advertiserId,
        ], $reportParams);

        // Add cache key based on parameters for performance
        $cacheKey = "gmv_max_report_" . md5(serialize($params));

        return $this->getCachedOrFetch(
            $cacheKey,
            fn() => $this->makeRequest('GET', '/gmv_max/report/get/', $params),
            300 // 5 minutes cache for reports
        );
    }

    /**
     * Get Product GMV Max Campaign Reports
     * @throws Exception
     */
    public function getProductGmvMaxReports(
        array  $storeIds,
        string $startDate,
        string $endDate,
        array  $metrics,
        array  $dimensions = ['campaign_id'],
        array  $options = []
    ): array
    {
        $params = array_merge([
            'store_ids' => json_encode($storeIds, JSON_THROW_ON_ERROR), // CRITICAL FIX: Encode as JSON string
            'start_date' => $startDate,
            'end_date' => $endDate,
            'metrics' => json_encode($metrics, JSON_THROW_ON_ERROR), // Also encode metrics as JSON
            'dimensions' => json_encode($dimensions, JSON_THROW_ON_ERROR), // Also encode dimensions as JSON
            'filtering' => json_encode(array_merge([
                'gmv_max_promotion_types' => ['PRODUCT']
            ], $options['filtering'] ?? []), JSON_THROW_ON_ERROR)
        ], array_diff_key($options, ['filtering' => ''])); // Remove filtering from options to avoid duplication

        return $this->getCampaignReports($params);
    }

    /**
     * Get LIVE GMV Max Campaign Reports
     * @throws Exception
     */
    public function getLiveGmvMaxReports(
        array  $storeIds,
        string $startDate,
        string $endDate,
        array  $metrics,
        array  $dimensions = ['campaign_id'],
        array  $options = []
    ): array
    {
        $params = array_merge([
            'store_ids' => json_encode($storeIds, JSON_THROW_ON_ERROR), // CRITICAL FIX: Encode as JSON string
            'start_date' => $startDate,
            'end_date' => $endDate,
            'metrics' => json_encode($metrics, JSON_THROW_ON_ERROR), // Also encode metrics as JSON
            'dimensions' => json_encode($dimensions, JSON_THROW_ON_ERROR), // Also encode dimensions as JSON
            'filtering' => json_encode(array_merge([
                'gmv_max_promotion_types' => ['LIVE']
            ], $options['filtering'] ?? []), JSON_THROW_ON_ERROR)
        ], array_diff_key($options, ['filtering' => ''])); // Remove filtering from options to avoid duplication

        return $this->getCampaignReports($params);
    }

    /**
     * Get Daily Performance Reports
     * @throws Exception
     */
    public function getDailyPerformanceReports(
        array  $storeIds,
        string $startDate,
        string $endDate,
        array  $campaignIds = [],
        array  $options = []
    ): array
    {
        $metrics = [
            'cost', 'net_cost', 'orders', 'cost_per_order',
            'gross_revenue', 'roi', 'product_impressions',
            'product_clicks', 'product_click_rate'
        ];

        $params = array_merge([
            'store_ids' => $storeIds,
            'start_date' => $startDate,
            'end_date' => $endDate,
            'metrics' => $metrics,
            'dimensions' => ['campaign_id', 'stat_time_day'],
            'enable_total_metrics' => true
        ], $options);

        if (!empty($campaignIds)) {
            $params['filtering']['campaign_ids'] = $campaignIds;
        }

        return $this->getCampaignReports($params);
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Validate GMV Max report parameters
     * @throws InvalidArgumentException|JsonException
     */
    private function validateReportParams(array $params): void
    {
        // Required parameters
        $required = ['store_ids', 'start_date', 'end_date', 'metrics', 'dimensions'];

        foreach ($required as $field) {
            if (!isset($params[$field])) {
                throw new InvalidArgumentException("Missing required parameter: {$field}");
            }
        }

        // Validate store_ids (can be array or JSON string)
        if (is_string($params['store_ids'])) {
            // If it's a JSON string, decode and validate
            $storeIdsArray = json_decode($params['store_ids'], true, 512, JSON_THROW_ON_ERROR);
            if (!is_array($storeIdsArray) || empty($storeIdsArray)) {
                throw new InvalidArgumentException('store_ids must be a non-empty array or valid JSON array string');
            }
            if (count($storeIdsArray) > 1) {
                throw new InvalidArgumentException('store_ids max size is 1');
            }
        } elseif (is_array($params['store_ids'])) {
            // If it's an array, validate directly
            if (empty($params['store_ids'])) {
                throw new InvalidArgumentException('store_ids must be a non-empty array');
            }
            if (count($params['store_ids']) > 1) {
                throw new InvalidArgumentException('store_ids max size is 1');
            }
        } else {
            throw new InvalidArgumentException('store_ids must be an array or JSON array string');
        }

        // Validate date format
        if (!$this->isValidDate($params['start_date']) || !$this->isValidDate($params['end_date'])) {
            throw new InvalidArgumentException('Dates must be in YYYY-MM-DD format');
        }

        // Validate metrics (can be array or JSON string)
        if (is_string($params['metrics'])) {
            $metricsArray = json_decode($params['metrics'], true, 512, JSON_THROW_ON_ERROR);
            if (!is_array($metricsArray) || empty($metricsArray)) {
                throw new InvalidArgumentException('metrics must be a non-empty array or valid JSON array string');
            }
        } elseif (!is_array($params['metrics']) || empty($params['metrics'])) {
            throw new InvalidArgumentException('metrics must be a non-empty array');
        }

        // Validate dimensions (can be array or JSON string)
        if (is_string($params['dimensions'])) {
            $dimensionsArray = json_decode($params['dimensions'], true, 512, JSON_THROW_ON_ERROR);
            if (!is_array($dimensionsArray) || empty($dimensionsArray)) {
                throw new InvalidArgumentException('dimensions must be a non-empty array or valid JSON array string');
            }
        } elseif (!is_array($params['dimensions']) || empty($params['dimensions'])) {
            throw new InvalidArgumentException('dimensions must be a non-empty array');
        }

        // Validate date range based on dimensions
        $this->validateDateRange($params);
    }

    /**
     * Validate date range based on dimensions
     * @throws InvalidArgumentException
     * @throws Exception
     */
    private function validateDateRange(array $params): void
    {
        $startDate = new DateTime($params['start_date']);
        $endDate = new DateTime($params['end_date']);
        $daysDiff = $startDate->diff($endDate)->days;

        // Handle dimensions as array or JSON string
        $dimensions = $params['dimensions'];
        if (is_string($dimensions)) {
            $dimensions = json_decode($dimensions, true, 512, JSON_THROW_ON_ERROR);
        }

        if (in_array('stat_time_hour', $dimensions, true) && $daysDiff > 1) {
            throw new InvalidArgumentException('Hourly breakdown data can only query up to 1 day');
        }

        if (in_array('stat_time_day', $dimensions, true) && $daysDiff > 30) {
            throw new InvalidArgumentException('Daily breakdown data can only query up to 30 days');
        }

        if (!in_array('stat_time_day', $dimensions, true) && !in_array('stat_time_hour', $dimensions, true) && $daysDiff > 365) {
            throw new InvalidArgumentException('Date range without time breakdown can only query up to 365 days');
        }
    }

    /**
     * Check if date string is valid YYYY-MM-DD format
     */
    private function isValidDate(string $date): bool
    {
        $d = DateTime::createFromFormat('Y-m-d', $date);
        return $d && $d->format('Y-m-d') === $date;
    }

    /**
     * Get available metrics for Product GMV Max campaigns
     */
    public function getProductGmvMaxMetrics(): array
    {
        return [
            'cost', 'net_cost', 'orders', 'cost_per_order', 'gross_revenue', 'roi'
        ];
    }

    /**
     * Get available metrics for LIVE GMV Max campaigns
     */
    public function getLiveGmvMaxMetrics(): array
    {
        return [
            'cost', 'net_cost', 'orders', 'cost_per_order', 'gross_revenue', 'roi',
            'live_views', 'cost_per_live_view', '10_second_live_views',
            'cost_per_10_second_live_view', 'live_follows'
        ];
    }

    /**
     * Get available dimensions for GMV Max reports
     */
    public function getAvailableDimensions(): array
    {
        return [
            'advertiser_id', 'campaign_id', 'stat_time_day', 'stat_time_hour',
            'item_group_id', 'item_id', 'room_id', 'duration'
        ];
    }

    /**
     * Get default campaign fields for optimized performance
     */
    private function getDefaultCampaignFields(): array
    {
        return [
            'advertiser_id',
            'campaign_id',
            'campaign_name',
            'operation_status',
            'create_time',
            'modify_time',
            'objective_type',
            'secondary_status',
            'roi_protection_compensation_status'
        ];
    }

    /**
     * Get all available campaign fields
     */
    private function getAllCampaignFields(): array
    {
        return [
            'advertiser_id',
            'campaign_id',
            'campaign_name',
            'operation_status',
            'create_time',
            'modify_time',
            'objective_type',
            'secondary_status',
            'roi_protection_compensation_status'
        ];
    }

    /**
     * Validate datetime format for TikTok API
     */
    private function isValidDateTimeFormat(string $datetime): bool
    {
        $format = 'Y-m-d H:i:s';
        $d = DateTime::createFromFormat($format, $datetime);
        return $d && $d->format($format) === $datetime;
    }

    /**
     * Map TikTok campaign operation status to local status
     */
    public function mapCampaignOperationStatus(string $tikTokStatus): string
    {
        $statusMap = [
            'ENABLE' => 'active',
            'DISABLE' => 'inactive'
        ];

        return $statusMap[$tikTokStatus] ?? 'unknown';
    }

    /**
     * Map TikTok primary status to local status
     */
    public function mapCampaignPrimaryStatus(string $tikTokStatus): string
    {
        $statusMap = [
            'STATUS_DELIVERY_OK' => 'active',
            'STATUS_DISABLE' => 'inactive',
            'STATUS_DELETE' => 'deleted'
        ];

        return $statusMap[$tikTokStatus] ?? 'unknown';
    }

    /**
     * Map local status to TikTok primary status
     */
    public function mapLocalStatusToPrimary(string $localStatus): string
    {
        $statusMap = [
            'active' => 'STATUS_DELIVERY_OK',
            'inactive' => 'STATUS_DISABLE',
            'disabled' => 'STATUS_DISABLE',
            'deleted' => 'STATUS_DELETE'
        ];

        return $statusMap[$localStatus] ?? 'STATUS_DISABLE';
    }

    /**
     * Process campaign data from TikTok API response
     */
    public function processCampaignData(array $tikTokCampaign): array
    {
        return [
            'campaign_id' => $tikTokCampaign['campaign_id'] ?? null,
            'name' => $tikTokCampaign['campaign_name'] ?? null,
            'operation_status' => $tikTokCampaign['operation_status'] ?? null,
            'primary_status' => $tikTokCampaign['primary_status'] ?? null,
            'secondary_status' => $tikTokCampaign['secondary_status'] ?? null,
            'roi_protection_status' => $tikTokCampaign['roi_protection_compensation_status'] ?? null,
            'objective_type' => $tikTokCampaign['objective_type'] ?? 'PRODUCT_SALES',
            'advertiser_id' => $tikTokCampaign['advertiser_id'] ?? null,
            'create_time' => $tikTokCampaign['create_time'] ?? null,
            'modify_time' => $tikTokCampaign['modify_time'] ?? null,
            'mapped_status' => $this->mapCampaignOperationStatus($tikTokCampaign['operation_status'] ?? 'DISABLE'),
            'mapped_primary_status' => $this->mapCampaignPrimaryStatus($tikTokCampaign['primary_status'] ?? 'STATUS_DISABLE')
        ];
    }

    /**
     * Clear all TikTok API caches
     */
    public function clearAllCaches(): void
    {
        $patterns = [
            "tiktok_campaigns_{$this->advertiserId}_*",
            "tiktok_stores_{$this->advertiserId}",
            "tiktok_identities_{$this->advertiserId}",
            "tiktok_videos_{$this->advertiserId}_*",
            "tiktok_custom_videos_{$this->advertiserId}",
            "tiktok_occupied_ads_{$this->advertiserId}",
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        if ($this->hasConfigurationError()) {
            return $this->getLastError();
        }

        // Test connection bằng cách gọi API đơn giản không cần advertiserId
        try {
            $response = $this->makeRequest('GET', '/advertiser/info/');

            if (ErrorHandler::isSuccess($response)) {
                return ErrorHandler::createSuccessResponse([], 'TikTok API connection successful');
            }

            return $response;
        } catch (Exception $e) {
            return ErrorHandler::createErrorResponse(
                'TikTok API connection failed: ' . $e->getMessage()
            );
        }
    }

    /**
     * Check if service has configuration errors
     */
    public function hasConfigurationError(): bool
    {
        return $this->lastError !== null;
    }

    /**
     * Get last configuration error
     */
    public function getLastError(): ?array
    {
        return $this->lastError;
    }

    /**
     * Check if service is properly configured
     */
    public function isConfigured(): bool
    {
        return !$this->hasConfigurationError();
    }

    /**
     * Get authorized advertiser accounts
     * Endpoint: /v1.3/oauth2/advertiser/get/
     */
    public function getAdvertiserAccounts(): array
    {
        if ($this->hasConfigurationError()) {
            return $this->getLastError();
        }

        return $this->getCachedOrFetch(
            'tiktok_advertiser_accounts',
            fn() => $this->makeRequest('GET', '/oauth2/advertiser/get/', [
                'app_id' => $this->appId,
                'secret' => $this->appSecret,
            ]) // Cache for 1 hour
        );
    }


    /**
     * Get API rate limit status
     */
    public function getRateLimitStatus(): array
    {
        // This would typically come from response headers
        // Implementation depends on TikTok's rate limiting headers
        return [
            'remaining' => 1000,
            'reset_time' => now()->addHour(),
            'delay' => $this->rateLimitDelay
        ];
    }
}
