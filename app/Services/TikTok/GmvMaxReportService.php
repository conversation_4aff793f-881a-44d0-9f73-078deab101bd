<?php

namespace App\Services\TikTok;

use App\Helpers\ErrorHandler;
use App\Models\Campaign;
use App\Models\CampaignReport;
use App\Models\Shop;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use RuntimeException;

/**
 * GMV Max Report Service
 * Specialized service for handling GMV Max campaign reports
 */
class GmvMaxReportService
{
    private TikTokApiService $apiService;

    public function __construct(TikTokApiService $apiService)
    {
        $this->apiService = $apiService;
    }

    /**
     * Set advertiser ID for a specific store
     *
     * @param string $storeId TikTok Store ID
     * @return bool True if advertiser ID was found and set, false otherwise
     */
    public function setAdvertiserIdForStore(string $storeId): bool
    {
        try {
            // Find shop by shop_id (which is the TikTok Store ID)
            $shop = Shop::where('shop_id', $storeId)->first();

            if (!$shop || !$shop->advertiser_id) {
                Log::warning('No advertiser_id found for store', [
                    'store_id' => $storeId,
                    'shop_found' => $shop ? 'yes' : 'no',
                    'advertiser_id' => $shop->advertiser_id ?? 'null'
                ]);
                return false;
            }

            // Set advertiser ID for API service
            $this->apiService->setAdvertiserId($shop->advertiser_id);

            Log::info('Set advertiser_id for store', [
                'store_id' => $storeId,
                'advertiser_id' => $shop->advertiser_id,
                'shop_name' => $shop->name
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to set advertiser_id for store', [
                'store_id' => $storeId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Sync GMV Max campaign reports for specified date range
     */
    public function syncReports(
        array  $storeIds,
        string $startDate,
        string $endDate,
        array  $campaignIds = []
    ): array
    {
        try {
            Log::info('Starting GMV Max reports sync', [
                'store_ids' => $storeIds,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'campaign_ids' => $campaignIds
            ]);

            $syncResults = [
                'success' => true,
                'total_synced' => 0,
                'created' => 0,
                'updated' => 0,
                'errors' => [],
                'stores_processed' => 0,
                'stores_failed' => 0
            ];

            // Loop through each store ID (TikTok API only accepts 1 store ID per request)
            foreach ($storeIds as $storeId) {
                try {
                    Log::info("Processing store ID: {$storeId}");

                    // CRITICAL FIX: Set advertiser_id for this store before making API calls
                    if (!$this->setAdvertiserIdForStore($storeId)) {
                        $syncResults['stores_failed']++;
                        $syncResults['errors'][] = "Store {$storeId}: No advertiser_id found";
                        Log::error("Skipping store {$storeId}: No advertiser_id found");
                        continue;
                    }

                    // Get Product GMV Max reports for this store
                    $productReports = $this->syncProductReports($storeId, $startDate, $endDate, $campaignIds);
                    $syncResults['total_synced'] += $productReports['synced'];
                    $syncResults['created'] += $productReports['created'];
                    $syncResults['updated'] += $productReports['updated'];

                    // Get LIVE GMV Max reports for this store
                    $liveReports = $this->syncLiveReports($storeId, $startDate, $endDate, $campaignIds);
                    $syncResults['total_synced'] += $liveReports['synced'];
                    $syncResults['created'] += $liveReports['created'];
                    $syncResults['updated'] += $liveReports['updated'];

                    $syncResults['stores_processed']++;
                    Log::info("Successfully processed store ID: {$storeId}");

                } catch (Exception $e) {
                    $syncResults['stores_failed']++;
                    $syncResults['errors'][] = "Store {$storeId}: " . $e->getMessage();
                    Log::error("Failed to process store ID: {$storeId}", [
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // Continue with next store ID
                    continue;
                }
            }

            Log::info('GMV Max reports sync completed', $syncResults);

            return $syncResults;

        } catch (Exception $e) {
            Log::error('GMV Max reports sync failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'error_type' => 'sync_error'
            ];
        }
    }

    /**
     * Sync Product GMV Max campaign reports for a single store
     * @throws Exception
     */
    private function syncProductReports(
        string $storeId,
        string $startDate,
        string $endDate,
        array  $campaignIds = []
    ): array
    {
        $metrics = $this->apiService->getProductGmvMaxMetrics();
        $dimensions = ['campaign_id', 'stat_time_day'];

        $options = [];
        if (!empty($campaignIds)) {
            $options['filtering']['campaign_ids'] = $campaignIds;
        }

        $response = $this->apiService->getProductGmvMaxReports(
            [$storeId], // Convert single store ID to array for API
            $startDate,
            $endDate,
            $metrics,
            $dimensions,
            $options
        );

        return $this->processReportResponse($response, 'PRODUCT');
    }

    /**
     * Sync LIVE GMV Max campaign reports for a single store
     * @throws Exception
     */
    private function syncLiveReports(
        string $storeId,
        string $startDate,
        string $endDate,
        array  $campaignIds = []
    ): array
    {
        $metrics = $this->apiService->getLiveGmvMaxMetrics();
        $dimensions = ['campaign_id', 'stat_time_day'];

        $options = [];
        if (!empty($campaignIds)) {
            $options['filtering']['campaign_ids'] = $campaignIds;
        }

        $response = $this->apiService->getLiveGmvMaxReports(
            [$storeId], // Convert single store ID to array for API
            $startDate,
            $endDate,
            $metrics,
            $dimensions,
            $options
        );

        return $this->processReportResponse($response, 'LIVE');
    }

    /**
     * Process API response and save to database
     */
    private function processReportResponse(array $response, string $campaignType): array
    {
        $created = 0;
        $updated = 0;
        $synced = 0;

        if (!isset($response['data']['list'])) {
            return ['synced' => 0, 'created' => 0, 'updated' => 0];
        }

        foreach ($response['data']['list'] as $reportData) {
            try {
                $result = $this->saveReportData($reportData, $campaignType);

                if ($result['action'] === 'created') {
                    $created++;
                } elseif ($result['action'] === 'updated') {
                    $updated++;
                }

                $synced++;

            } catch (Exception $e) {
                Log::warning('Failed to save report data', [
                    'report_data' => $reportData,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'synced' => $synced,
            'created' => $created,
            'updated' => $updated
        ];
    }

    /**
     * Save individual report data to database
     */
    private function saveReportData(array $reportData, string $campaignType): array
    {
        $dimensions = $reportData['dimensions'] ?? [];
        $metrics = $reportData['metrics'] ?? [];

        $campaignId = $dimensions['campaign_id'] ?? null;
        $reportDate = $dimensions['stat_time_day'] ?? null;

        if (!$campaignId || !$reportDate) {
            throw new RuntimeException('Missing required dimensions: campaign_id or stat_time_day');
        }

        // Ensure campaign exists before saving report
        $this->ensureCampaignExists($campaignId, $campaignType);

        // Find or create campaign report
        $reportId = $this->generateReportId($campaignId, $reportDate, $campaignType);

        $reportData = [
            'report_id' => $reportId,
            'campaign_id' => $campaignId,
            'report_date' => Carbon::parse($reportDate),
            'total_cost' => $metrics['cost'] ?? 0,
            'orders_count' => $metrics['orders'] ?? 0,
            'gross_revenue' => $metrics['gross_revenue'] ?? 0,
            'cost_per_order' => $metrics['cost_per_order'] ?? 0,
            'roi' => $metrics['roi'] ?? 0,
            'impressions' => $metrics['product_impressions'] ?? $metrics['live_views'] ?? 0,
            'clicks' => $metrics['product_clicks'] ?? 0,
            'ctr' => $metrics['product_click_rate'] ?? 0,
            'conversion_rate' => $metrics['ad_conversion_rate'] ?? 0,
        ];

        $existingReport = CampaignReport::where('report_id', $reportId)->first();

        if ($existingReport) {
            $existingReport->update($reportData);
            return ['action' => 'updated', 'report' => $existingReport];
        }

        $newReport = CampaignReport::create($reportData);
        return ['action' => 'created', 'report' => $newReport];
    }

    /**
     * Ensure campaign exists in database before saving reports
     *
     * @param string $campaignId TikTok Campaign ID
     * @param string $campaignType PRODUCT or LIVE
     * @return void
     */
    private function ensureCampaignExists(string $campaignId, string $campaignType): void
    {
        try {
            // Check if campaign already exists
            $existingCampaign = Campaign::where('campaign_id', $campaignId)->first();

            if ($existingCampaign) {
                return; // Campaign already exists
            }

            // Get campaign info from TikTok API
            $campaignInfo = $this->apiService->getCampaignInfo($campaignId);

            if (!ErrorHandler::isSuccess($campaignInfo)) {
                Log::warning('Could not fetch campaign info from TikTok API', [
                    'campaign_id' => $campaignId,
                    'error' => $campaignInfo['message'] ?? 'Unknown error'
                ]);

                // Create minimal campaign record with available data
                $this->createMinimalCampaign($campaignId, $campaignType);
                return;
            }

            // Create campaign from TikTok API data
            $campaignData = $campaignInfo['data'] ?? [];
            $this->createCampaignFromApiData($campaignId, $campaignData, $campaignType);

        } catch (Exception $e) {
            Log::warning('Failed to ensure campaign exists', [
                'campaign_id' => $campaignId,
                'error' => $e->getMessage()
            ]);

            // Fallback: create minimal campaign record
            $this->createMinimalCampaign($campaignId, $campaignType);
        }
    }

    /**
     * Create minimal campaign record when API data is not available
     */
    private function createMinimalCampaign(string $campaignId, string $campaignType): void
    {
        Campaign::create([
            'campaign_id' => $campaignId,
            'name' => "Campaign {$campaignId}",
            'status' => 'active',
            'advertiser_id' => $this->apiService->getAdvertiserId() ?? 'unknown',
            'shop_id' => 1, // Default shop ID - you may need to adjust this
            'start_date' => now(),
        ]);

        Log::info('Created minimal campaign record', [
            'campaign_id' => $campaignId,
            'type' => $campaignType
        ]);
    }

    /**
     * Create campaign from TikTok API data
     */
    private function createCampaignFromApiData(string $campaignId, array $campaignData, string $campaignType): void
    {
        $campaignRecord = [
            'campaign_id' => $campaignId,
            'name' => $campaignData['campaign_name'] ?? "Campaign {$campaignId}",
            'status' => 'active',
            'operation_status' => $campaignData['operation_status'] ?? 'ENABLE',
            'primary_status' => $campaignData['primary_status'] ?? null,
            'secondary_status' => $campaignData['secondary_status'] ?? null,
            'objective_type' => $campaignData['objective_type'] ?? null,
            'advertiser_id' => $campaignData['advertiser_id'] ?? $this->apiService->getAdvertiserId(),
            'shop_id' => 1, // Default shop ID - you may need to adjust this
            'budget' => $campaignData['budget'] ?? null,
            'target_roi' => $campaignData['roas_bid'] ?? null,
            'start_date' => isset($campaignData['schedule_start_time'])
                ? Carbon::parse($campaignData['schedule_start_time'])
                : now(),
            'end_date' => isset($campaignData['schedule_end_time'])
                ? Carbon::parse($campaignData['schedule_end_time'])
                : null,
        ];

        Campaign::create($campaignRecord);

        Log::info('Created campaign from API data', [
            'campaign_id' => $campaignId,
            'campaign_name' => $campaignRecord['name'],
            'type' => $campaignType
        ]);
    }

    /**
     * Map TikTok operation status to our status enum
     */
    private function mapOperationStatus(string $operationStatus): string
    {
        return match ($operationStatus) {
            'ENABLE' => 'active',
            'DISABLE' => 'paused',
            'DELETE' => 'cancelled',
            default => 'draft'
        };
    }

    /**
     * Generate unique report ID with consistent format
     * Format: gmv_max_{type}_{campaign_id}_{YYYYMMDD}
     * Example: gmv_max_PRODUCT_1837859149890578_20250724
     */
    private function generateReportId(string $campaignId, string $reportDate, string $campaignType): string
    {
        // Normalize date to YYYYMMDD format (remove time and dashes)
        $normalizedDate = \Carbon\Carbon::parse($reportDate)->format('Ymd');

        return "gmv_max_{$campaignType}_{$campaignId}_{$normalizedDate}";
    }

    /**
     * Get performance summary for campaigns
     */
    public function getPerformanceSummary(array $campaignIds, int $days = 30): array
    {
        $startDate = Carbon::now()->subDays($days);

        $reports = CampaignReport::whereIn('campaign_id', $campaignIds)
            ->where('report_date', '>=', $startDate)
            ->get();

        return [
            'total_cost' => $reports->sum('total_cost'),
            'total_revenue' => $reports->sum('gross_revenue'),
            'total_orders' => $reports->sum('orders_count'),
            'average_roi' => $reports->avg('roi'),
            'average_ctr' => $reports->avg('ctr'),
            'average_conversion_rate' => $reports->avg('conversion_rate'),
            'profit' => $reports->sum('gross_revenue') - $reports->sum('total_cost'),
            'report_count' => $reports->count(),
            'date_range' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => Carbon::now()->format('Y-m-d')
            ]
        ];
    }

    /**
     * Get top performing campaigns
     */
    public function getTopPerformingCampaigns(int $limit = 10, int $days = 30): CampaignReport
    {
        $startDate = Carbon::now()->subDays($days);

        return CampaignReport::select('campaign_id')
            ->selectRaw('AVG(roi) as avg_roi')
            ->selectRaw('SUM(gross_revenue) as total_revenue')
            ->selectRaw('SUM(total_cost) as total_cost')
            ->selectRaw('SUM(orders_count) as total_orders')
            ->where('report_date', '>=', $startDate)
            ->groupBy('campaign_id')
            ->orderByDesc('avg_roi')
            ->limit($limit)
            ->get();
    }

    /**
     * Get campaigns that need optimization
     */
    public function getCampaignsNeedingOptimization(int $days = 7): CampaignReport
    {
        $startDate = Carbon::now()->subDays($days);

        return CampaignReport::select('campaign_id')
            ->selectRaw('AVG(roi) as avg_roi')
            ->selectRaw('AVG(ctr) as avg_ctr')
            ->selectRaw('AVG(conversion_rate) as avg_conversion_rate')
            ->where('report_date', '>=', $startDate)
            ->groupBy('campaign_id')
            ->havingRaw('AVG(roi) < 50 OR AVG(ctr) < 1 OR AVG(conversion_rate) < 1')
            ->orderBy('avg_roi')
            ->get();
    }
}
