<?php

namespace App\Helpers;

use Carbon\Carbon;

class DateHelper
{
    /**
     * Vietnamese day names mapping
     */
    private static array $vietnameseDayNames = [
        'Monday' => 'Thứ Hai',
        'Tuesday' => 'Thứ Ba',
        'Wednesday' => 'Thứ Tư',
        'Thursday' => 'Thứ Năm',
        'Friday' => 'Thứ Sáu',
        'Saturday' => 'Thứ Bảy',
        'Sunday' => 'Chủ Nhật'
    ];

    /**
     * Format date with Vietnamese day names for tooltip
     *
     * @param Carbon|null $date
     * @param string $format
     * @return string
     */
    public static function formatVietnameseDate(?Carbon $date, string $format = 'd/m/Y'): string
    {
        if (!$date) return '';

        $englishDay = $date->format('l');
        $vietnameseDay = self::$vietnameseDayNames[$englishDay] ?? $englishDay;

        // Handle special format with Vietnamese text
        if (str_contains($format, 'lúc')) {
            // Split format around 'lúc' and format parts separately
            $parts = explode(' lúc ', $format);
            if (count($parts) === 2) {
                $datePart = $date->format($parts[0]);
                $timePart = $date->format($parts[1]);
                $formattedDate = "{$datePart} lúc {$timePart}";
            } else {
                $formattedDate = $date->format($format);
            }
        } else {
            $formattedDate = $date->format($format);
        }

        return "{$vietnameseDay}, {$formattedDate}";
    }

    /**
     * Get Vietnamese day name from English day name
     *
     * @param string $englishDay
     * @return string
     */
    public static function getVietnameseDayName(string $englishDay): string
    {
        return self::$vietnameseDayNames[$englishDay] ?? $englishDay;
    }

    /**
     * Format date for table display (Vietnamese format)
     *
     * @param Carbon|null $date
     * @return string
     */
    public static function formatTableDate(?Carbon $date): string
    {
        return $date ? $date->format('d/m/Y') : '';
    }

    /**
     * Format datetime for table display (Vietnamese format)
     *
     * @param Carbon|null $date
     * @return string
     */
    public static function formatTableDateTime(?Carbon $date): string
    {
        return $date ? $date->format('d/m/Y H:i') : '';
    }

    /**
     * Create tooltip closure for Filament columns
     *
     * @param string $format
     * @return \Closure
     */
    public static function tooltipClosure(string $format = 'd/m/Y lúc H:i:s'): \Closure
    {
        return fn ($record, $column) => self::formatVietnameseDate($record->{$column->getName()}, $format);
    }
}
