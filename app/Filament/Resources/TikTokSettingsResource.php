<?php

namespace App\Filament\Resources;

use App\Filament\Resources\TikTokSettingsResource\Actions\ClearCacheAction;
use App\Filament\Resources\TikTokSettingsResource\Actions\TestAiConnectionAction;
use App\Filament\Resources\TikTokSettingsResource\Actions\TestConnectionAction;
use App\Filament\Resources\TikTokSettingsResource\Actions\TestSettingAction;
use App\Filament\Resources\TikTokSettingsResource\Pages;
use App\Models\TikTokSettings;
use App\Helpers\DateHelper;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\BadgeColumn;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;

class TikTokSettingsResource extends Resource
{
    protected static ?string $model = TikTokSettings::class;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static ?string $navigationGroup = 'Cấu hình hệ thống';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Cài đặt TikTok API';

    protected static ?string $pluralModelLabel = 'Cài đặt TikTok API';public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        TextInput::make('key')
                            ->label('Configuration Key')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255)
                            ->helperText('Unique identifier for this setting (e.g., api.access_token)'),

                        Select::make('group')
                            ->label('Nhóm cấu hình')
                            ->options([
                                'api' => 'API Configuration',
                                'sync' => 'Sync Settings',
                                'cache' => 'Cache Settings',
                                'logging' => 'Logging Settings',
                                'ai_scoring' => 'AI Scoring & Analysis',
                                'general' => 'General Settings',
                            ])
                            ->default('general')
                            ->required(),

                        Textarea::make('description')
                            ->label('Mô tả')
                            ->maxLength(500)
                            ->rows(2),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Giá trị cấu hình')
                    ->schema([
                        Select::make('type')
                            ->label('Kiểu dữ liệu')
                            ->options([
                                'string' => 'String',
                                'boolean' => 'Boolean',
                                'integer' => 'Integer',
                                'float' => 'Float',
                                'json' => 'JSON',
                                'array' => 'Array',
                            ])
                            ->default('string')
                            ->required()
                            ->reactive(),

                        TextInput::make('value')
                            ->label('Giá trị')
                            ->required()
                            ->maxLength(1000)
                            ->password(fn(callable $get) => $get('is_encrypted'))
                            ->helperText('Giá trị sẽ được mã hóa nếu đánh dấu "Encrypted"'),

                        TextInput::make('default_value')
                            ->label('Giá trị mặc định')
                            ->maxLength(1000),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Tùy chọn bảo mật')
                    ->schema([
                        Toggle::make('is_encrypted')
                            ->label('Mã hóa giá trị')
                            ->helperText('Bật để mã hóa giá trị nhạy cảm như access token'),

                        Toggle::make('is_public')
                            ->label('Hiển thị công khai')
                            ->default(true)
                            ->helperText('Tắt để ẩn giá trị trong giao diện admin'),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Validation Rules')
                    ->schema([
                        TagsInput::make('validation_rules')
                            ->label('Quy tắc validation')
                            ->suggestions([
                                'required',
                                'string',
                                'integer',
                                'boolean',
                                'numeric',
                                'array',
                                'min:1',
                                'max:100',
                                'in:debug,info,warning,error',
                                'in:gemini,openai,claude',
                                'in:realtime,hourly,daily',
                                'min:0',
                                'max:1',
                                'min:10',
                                'max:10000',
                            ])
                            ->helperText('Laravel validation rules (e.g., required, string, min:10)'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('key')
                    ->label('Configuration Key')
                    ->searchable()
                    ->sortable()
                    ->copyable(),

                BadgeColumn::make('group')
                    ->label('Nhóm')
                    ->colors([
                        'primary' => 'api',
                        'success' => 'sync',
                        'warning' => 'cache',
                        'info' => 'logging',
                        'purple' => 'ai_scoring',
                        'secondary' => 'general',
                    ]),

                TextColumn::make('value')
                    ->label('Giá trị')
                    ->limit(30)
                    ->formatStateUsing(function ($state, $record) {
                        if (!$record->is_public) {
                            return '***';
                        }

                        if ($record->type === 'boolean') {
                            return $record->getTypedValue() ? 'True' : 'False';
                        }

                        return $state;
                    })
                    ->tooltip(function ($record) {
                        return $record->is_public ? $record->description : 'Giá trị bị ẩn';
                    }),

                BadgeColumn::make('type')
                    ->label('Kiểu')
                    ->colors([
                        'primary' => 'string',
                        'success' => 'boolean',
                        'warning' => 'integer',
                        'info' => 'json',
                    ]),

                IconColumn::make('is_encrypted')
                    ->label('Mã hóa')
                    ->boolean()
                    ->trueIcon('heroicon-o-lock-closed')
                    ->falseIcon('heroicon-o-lock-open'),

                IconColumn::make('is_public')
                    ->label('Công khai')
                    ->boolean()
                    ->trueIcon('heroicon-o-eye')
                    ->falseIcon('heroicon-o-eye-slash'),

                TextColumn::make('last_tested_at')
                    ->label('Test cuối')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(),

                TextColumn::make('updatedBy.name')
                    ->label('Cập nhật bởi')
                    ->toggleable(),

                TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                SelectFilter::make('group')
                    ->label('Nhóm cấu hình')
                    ->options([
                        'api' => 'API Configuration',
                        'sync' => 'Sync Settings',
                        'cache' => 'Cache Settings',
                        'logging' => 'Logging Settings',
                        'ai_scoring' => 'AI Scoring & Analysis',
                        'general' => 'General Settings',
                    ]),

                SelectFilter::make('type')
                    ->label('Kiểu dữ liệu')
                    ->options([
                        'string' => 'String',
                        'boolean' => 'Boolean',
                        'integer' => 'Integer',
                        'json' => 'JSON',
                    ]),

                Tables\Filters\TernaryFilter::make('is_encrypted')
                    ->label('Mã hóa'),

                Tables\Filters\TernaryFilter::make('is_public')
                    ->label('Công khai'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                TestSettingAction::make(),
            ])
            ->headerActions([
                TestConnectionAction::make(),
                TestAiConnectionAction::make(),
                ClearCacheAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('group')
            ->groups([
                Tables\Grouping\Group::make('group')
                    ->label('Nhóm cấu hình')
                    ->collapsible(),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTikTokSettings::route('/'),
            'create' => Pages\CreateTikTokSettings::route('/create'),
            'edit' => Pages\EditTikTokSettings::route('/{record}/edit'),
        ];
    }
}
