<?php

namespace App\Filament\Resources\VideoResource\Actions;

use App\Models\Video;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use App\Jobs\SyncVideosJob;
use App\Helpers\ErrorHandler;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;

class SyncVideosAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'sync_videos';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Sync Campaign Videos')
            ->icon('heroicon-o-arrow-path')
            ->color('warning')
            ->requiresConfirmation()
            ->modalHeading('Sync Videos from TikTok API')
            ->modalDescription('This will fetch and sync videos for this video\'s campaign from TikTok API.')
            ->modalSubmitActionLabel('Start Sync')
            ->visible(fn(Video $record) => $record->campaign_id)
            ->action(function (Video $record) {
                $this->syncVideos($record);
            });
    }

    protected function syncVideos(Video $record): void
    {
        try {
            // Validate that video has a campaign
            if (!$record->campaign_id || !$record->campaign) {
                Notification::make()
                    ->title('Cannot Sync Videos')
                    ->body('This video is not associated with a campaign')
                    ->warning()
                    ->send();
                return;
            }

            // Validate campaign has required data
            if (!$record->campaign->store_id || !$record->campaign->store_authorized_bc_id) {
                Notification::make()
                    ->title('Campaign Configuration Error')
                    ->body('Campaign is missing required store information for video sync.')
                    ->warning()
                    ->send();
                return;
            }

            // Use queue for better performance
            SyncVideosJob::dispatch(
                'single',
                $record->campaign->campaign_id,
                [],
                ['page_size' => 20, 'max_pages' => 5],
                true
            );

            Notification::make()
                ->title('Video Sync Queued')
                ->body("Video sync for campaign '{$record->campaign->name}' has been queued for background processing.")
                ->success()
                ->duration(8000)
                ->send();

            return; // Exit early since we're using queue

        } catch (\Exception $e) {
            Notification::make()
                ->title('Video Sync Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
