<?php

namespace App\Filament\Resources;

use App\Filament\Resources\ShopResource\Actions\SyncAllBulkAction;
use App\Filament\Resources\ShopResource\Actions\SyncAllShopsAction;
use App\Filament\Resources\ShopResource\Actions\SyncByAdvertiserAction;
use App\Filament\Resources\ShopResource\Actions\SyncWithTikTokAction;
use App\Filament\Resources\ShopResource\Pages;
use App\Models\Shop;
use App\Helpers\DateHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ShopResource extends Resource
{
    protected static ?string $model = Shop::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    protected static ?string $navigationGroup = 'Quản lý cửa hàng';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Cửa hàng';

    protected static ?string $pluralModelLabel = 'Cửa hàng';public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên cửa hàng')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('shop_id')
                    ->label('Shop ID')
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),
                Forms\Components\Select::make('region')
                    ->label('Khu vực')
                    ->options([
                        'VN' => 'Việt Nam',
                        'TH' => 'Thái Lan',
                        'MY' => 'Malaysia',
                        'SG' => 'Singapore',
                        'ID' => 'Indonesia',
                        'PH' => 'Philippines',
                    ]),
                Forms\Components\Select::make('currency')
                    ->label('Tiền tệ')
                    ->options([
                        'VND' => 'Việt Nam Đồng',
                        'USD' => 'US Dollar',
                        'THB' => 'Thai Baht',
                    ]),
                Forms\Components\Toggle::make('is_eligible_gmv_max')
                    ->label('Đủ điều kiện GMV Max')
                    ->default(false),
                Forms\Components\Select::make('advertiser_account_id')
                    ->label('Advertiser Account')
                    ->relationship('advertiserAccount', 'advertiser_name')
                    ->searchable()
                    ->preload()
                    ->createOptionForm([
                        Forms\Components\TextInput::make('advertiser_id')
                            ->label('Advertiser ID')
                            ->required(),
                        Forms\Components\TextInput::make('advertiser_name')
                            ->label('Advertiser Name')
                            ->required(),
                    ]),
                Forms\Components\TextInput::make('advertiser_id')
                    ->label('Advertiser ID (TikTok)')
                    ->maxLength(255)
                    ->helperText('ID từ TikTok API, sẽ được tự động cập nhật khi đồng bộ'),
                Forms\Components\TextInput::make('advertiser_name')
                    ->label('Advertiser Name')
                    ->maxLength(255)
                    ->helperText('Tên advertiser từ TikTok API'),
                Forms\Components\Select::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'suspended' => 'Tạm ngưng',
                        'pending' => 'Chờ xử lý',
                    ])
                    ->default('pending')
                    ->required(),
                Forms\Components\Select::make('exclusive_authorization_status')
                    ->label('Trạng thái ủy quyền độc quyền')
                    ->options([
                        'none' => 'Không có',
                        'pending' => 'Chờ xử lý',
                        'granted' => 'Đã cấp',
                        'expired' => 'Đã hết hạn',
                        'revoked' => 'Đã thu hồi',
                        'suspended' => 'Tạm dừng',
                    ])
                    ->default('none')
                    ->required(),

                Forms\Components\Section::make('Business Center Information')
                    ->description('Thông tin Business Center quản lý shop')
                    ->schema([
                        Forms\Components\TextInput::make('store_authorized_bc_id')
                            ->label('BC ID được ủy quyền')
                            ->maxLength(255)
                            ->placeholder('bc_123456789')
                            ->helperText('ID của Business Center được ủy quyền truy cập shop'),
                        Forms\Components\TextInput::make('bc_name')
                            ->label('Tên Business Center')
                            ->maxLength(255)
                            ->placeholder('Tên Business Center')
                            ->helperText('Tên của Business Center quản lý shop'),
                        Forms\Components\Toggle::make('is_owner_bc')
                            ->label('BC sở hữu shop')
                            ->helperText('BC có sở hữu shop hay chỉ là partner'),
                        Forms\Components\Select::make('user_role')
                            ->label('Vai trò user trong BC')
                            ->options([
                                'ADMIN' => 'Admin - Toàn quyền',
                                'STANDARD' => 'Standard - Hạn chế',
                            ])
                            ->placeholder('Chọn vai trò')
                            ->helperText('Vai trò của user trong Business Center'),
                        Forms\Components\Select::make('store_role')
                            ->label('Quyền đối với shop')
                            ->options([
                                'AD_PROMOTION' => 'Quảng cáo - Tạo ads',
                                'MANAGER' => 'Quản lý - Toàn quyền shop',
                                'UNSET' => 'Chưa thiết lập',
                            ])
                            ->placeholder('Chọn quyền')
                            ->helperText('Quyền của Business Center đối với shop này'),
                    ])
                    ->columns(2)
                    ->collapsible(),

                Forms\Components\Section::make('Shop Details')
                    ->description('Chi tiết bổ sung từ TikTok API')
                    ->schema([
                        Forms\Components\TextInput::make('store_code')
                            ->label('Store Code')
                            ->maxLength(255)
                            ->placeholder('SHOP001')
                            ->helperText('Mã code duy nhất của TikTok Shop'),
                        Forms\Components\TextInput::make('thumbnail_url')
                            ->label('Thumbnail URL')
                            ->url()
                            ->maxLength(500)
                            ->placeholder('https://example.com/thumbnail.jpg')
                            ->helperText('URL ảnh thumbnail của shop'),
                        Forms\Components\TagsInput::make('targeting_region_codes')
                            ->label('Mã vùng target')
                            ->placeholder('VN, TH, MY')
                            ->helperText('Danh sách mã vùng shop có thể target (VN=Vietnam, TH=Thailand, MY=Malaysia, etc.)')
                            ->suggestions(['VN', 'TH', 'MY', 'SG', 'ID', 'PH']),
                    ])
                    ->columns(2)
                    ->collapsible(),
            ]);
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Infolists\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Infolists\Components\TextEntry::make('name')
                            ->label('Tên cửa hàng'),
                        Infolists\Components\TextEntry::make('shop_id')
                            ->label('Shop ID')
                            ->copyable(),
                        Infolists\Components\TextEntry::make('store_code')
                            ->label('Store Code')
                            ->placeholder('N/A'),
                        Infolists\Components\TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'active' => 'success',
                                'inactive' => 'danger',
                                'suspended' => 'warning',
                                'pending' => 'secondary',
                                default => 'gray',
                            }),
                        Infolists\Components\IconEntry::make('is_eligible_gmv_max')
                            ->label('Đủ điều kiện GMV Max')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('region')
                            ->label('Khu vực chính')
                            ->badge(),
                        Infolists\Components\TextEntry::make('currency')
                            ->label('Tiền tệ')
                            ->badge(),
                    ])
                    ->columns(3),

                Infolists\Components\Section::make('Business Center')
                    ->schema([
                        Infolists\Components\TextEntry::make('bc_name')
                            ->label('Tên Business Center')
                            ->placeholder('N/A'),
                        Infolists\Components\TextEntry::make('store_authorized_bc_id')
                            ->label('BC ID được ủy quyền')
                            ->placeholder('N/A'),
                        Infolists\Components\IconEntry::make('is_owner_bc')
                            ->label('BC sở hữu shop')
                            ->boolean(),
                        Infolists\Components\TextEntry::make('user_role')
                            ->label('Vai trò user')
                            ->badge()
                            ->placeholder('N/A'),
                        Infolists\Components\TextEntry::make('store_role')
                            ->label('Vai trò store')
                            ->badge()
                            ->placeholder('N/A'),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Advertiser & Authorization')
                    ->schema([
                        Infolists\Components\TextEntry::make('advertiser_name')
                            ->label('Tên Advertiser')
                            ->placeholder('N/A'),
                        Infolists\Components\TextEntry::make('advertiser_id')
                            ->label('Advertiser ID')
                            ->copyable()
                            ->placeholder('N/A'),
                        Infolists\Components\TextEntry::make('exclusive_authorization_status')
                            ->label('Trạng thái ủy quyền')
                            ->badge()
                            ->color(fn(string $state): string => match ($state) {
                                'granted' => 'success',
                                'pending' => 'warning',
                                'expired' => 'danger',
                                'revoked' => 'danger',
                                'suspended' => 'info',
                                'none' => 'secondary',
                                default => 'gray',
                            }),
                    ])
                    ->columns(2),

                Infolists\Components\Section::make('Targeting & Details')
                    ->schema([
                        Infolists\Components\TextEntry::make('targeting_region_codes')
                            ->label('Vùng target')
                            ->formatStateUsing(function ($state) {
                                if (is_array($state) && !empty($state)) {
                                    return implode(', ', $state);
                                }
                                return 'N/A';
                            })
                            ->badge(),
                        Infolists\Components\ImageEntry::make('display_thumbnail')
                            ->label('Shop Thumbnail')
                            ->size(80)
                            ->defaultImageUrl(asset('images/default-shop.svg'))
                            ->hiddenLabel(false),
                        Infolists\Components\TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),
                        Infolists\Components\TextEntry::make('updated_at')
                            ->label('Cập nhật lần cuối')
                            ->dateTime('d/m/Y H:i'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('display_thumbnail')
                    ->label('Thumbnail')
                    ->circular()
                    ->size(40)
                    ->defaultImageUrl(asset('images/default-shop.svg'))
                    ->tooltip(fn($record) => $record->name),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên cửa hàng')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('shop_id')
                    ->label('Shop ID')
                    ->searchable()
                    ->copyable(),
                Tables\Columns\BadgeColumn::make('status')
                    ->label('Trạng thái')
                    ->colors([
                        'secondary' => 'pending',
                        'success' => 'active',
                        'danger' => 'inactive',
                        'warning' => 'suspended',
                    ])
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'suspended' => 'Tạm ngưng',
                        'pending' => 'Chờ xử lý',
                        default => $state,
                    }),
                Tables\Columns\IconColumn::make('is_eligible_gmv_max')
                    ->label('GMV Max')
                    ->boolean()
                    ->trueIcon('heroicon-o-check-circle')
                    ->falseIcon('heroicon-o-x-circle')
                    ->trueColor('success')
                    ->falseColor('danger'),
                Tables\Columns\TextColumn::make('region')
                    ->label('Khu vực chính')
                    ->badge()
                    ->color('info')
                    ->tooltip(function ($record) {
                        if (is_array($record->targeting_region_codes) && !empty($record->targeting_region_codes)) {
                            return 'Tất cả vùng: ' . implode(', ', $record->targeting_region_codes);
                        }
                        return 'Chỉ có vùng chính';
                    }),
                Tables\Columns\TextColumn::make('currency')
                    ->label('Tiền tệ')
                    ->badge()
                    ->color('gray'),
                Tables\Columns\TextColumn::make('advertiserAccount.advertiser_name')
                    ->label('Advertiser Account')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('advertiser_id')
                    ->label('Advertiser ID')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('advertiser_name')
                    ->label('Advertiser Name')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\BadgeColumn::make('exclusive_authorization_status')
                    ->label('Ủy quyền')
                    ->colors([
                        'secondary' => 'none',
                        'warning' => 'pending',
                        'success' => 'granted',
                        'danger' => ['expired', 'revoked'],
                        'info' => 'suspended',
                    ])
                    ->formatStateUsing(fn(string $state): string => match ($state) {
                        'none' => 'Không có',
                        'pending' => 'Chờ xử lý',
                        'granted' => 'Đã cấp',
                        'expired' => 'Đã hết hạn',
                        'revoked' => 'Đã thu hồi',
                        'suspended' => 'Tạm dừng',
                        default => $state,
                    }),
                Tables\Columns\TextColumn::make('bc_name')
                    ->label('Business Center')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\IconColumn::make('is_owner_bc')
                    ->label('BC Owner')
                    ->boolean()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('store_role')
                    ->label('Store Role')
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'AD_PROMOTION' => 'success',
                        'MANAGER' => 'info',
                        'UNSET' => 'gray',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn(?string $state): string => match ($state) {
                        'AD_PROMOTION' => 'Quảng cáo',
                        'MANAGER' => 'Quản lý',
                        'UNSET' => 'Chưa thiết lập',
                        null => 'N/A',
                        default => $state,
                    })
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('store_code')
                    ->label('Store Code')
                    ->searchable()
                    ->placeholder('N/A')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('targeting_region_codes')
                    ->label('Target Regions')
                    ->formatStateUsing(function ($state) {
                        if (is_array($state) && !empty($state)) {
                            return implode(', ', $state);
                        }
                        return 'N/A';
                    })
                    ->badge()
                    ->separator(',')
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->defaultSort('created_at', 'desc')
            ->searchable()
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'active' => 'Hoạt động',
                        'inactive' => 'Không hoạt động',
                        'suspended' => 'Tạm ngưng',
                        'pending' => 'Chờ xử lý',
                    ]),
                Tables\Filters\SelectFilter::make('region')
                    ->label('Khu vực')
                    ->options([
                        'VN' => 'Việt Nam',
                        'TH' => 'Thái Lan',
                        'MY' => 'Malaysia',
                        'SG' => 'Singapore',
                        'ID' => 'Indonesia',
                        'PH' => 'Philippines',
                    ]),
                Tables\Filters\TernaryFilter::make('is_eligible_gmv_max')
                    ->label('Đủ điều kiện GMV Max')
                    ->placeholder('Tất cả')
                    ->trueLabel('Đủ điều kiện')
                    ->falseLabel('Không đủ điều kiện'),
                Tables\Filters\SelectFilter::make('exclusive_authorization_status')
                    ->label('Trạng thái ủy quyền')
                    ->options([
                        'none' => 'Không có',
                        'pending' => 'Chờ xử lý',
                        'granted' => 'Đã cấp',
                        'expired' => 'Đã hết hạn',
                        'revoked' => 'Đã thu hồi',
                        'suspended' => 'Tạm dừng',
                    ]),
                Tables\Filters\TernaryFilter::make('is_owner_bc')
                    ->label('BC sở hữu shop')
                    ->placeholder('Tất cả')
                    ->trueLabel('BC sở hữu')
                    ->falseLabel('BC partner'),
                Tables\Filters\SelectFilter::make('store_role')
                    ->label('Vai trò store')
                    ->options([
                        'AD_PROMOTION' => 'Quảng cáo',
                        'MANAGER' => 'Quản lý',
                        'UNSET' => 'Chưa thiết lập',
                    ]),
                Tables\Filters\SelectFilter::make('exclusive_authorization_status')
                    ->label('Trạng thái ủy quyền')
                    ->options([
                        'none' => 'Không có',
                        'pending' => 'Chờ xử lý',
                        'granted' => 'Đã cấp',
                        'expired' => 'Đã hết hạn',
                        'revoked' => 'Đã thu hồi',
                    ]),
                Tables\Filters\SelectFilter::make('advertiserAccount')
                    ->label('Advertiser Account')
                    ->relationship('advertiserAccount', 'advertiser_name')
                    ->searchable()
                    ->preload(),
                Tables\Filters\TrashedFilter::make(),
            ])
            ->headerActions([
                SyncAllShopsAction::make(),
                SyncByAdvertiserAction::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\EditAction::make(),
                SyncWithTikTokAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    SyncAllBulkAction::make(),
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ShopResource\RelationManagers\CampaignsRelationManager::class,
            ShopResource\RelationManagers\ProductsRelationManager::class,
        ];
    }

    public static function getWidgets(): array
    {
        return [
            ShopResource\Widgets\OptimizedShopStatsWidget::class,
            ShopResource\Widgets\AuthorizationBreakdownWidget::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListShops::route('/'),
            'create' => Pages\CreateShop::route('/create'),
            'view' => Pages\ViewShop::route('/{record}'),
            'edit' => Pages\EditShop::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}
