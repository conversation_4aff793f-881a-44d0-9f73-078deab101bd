<?php

namespace App\Filament\Resources;

use App\Filament\Resources\SessionResource\Pages;
use App\Filament\Resources\SessionResource\RelationManagers;
use App\Models\Session;
use App\Helpers\DateHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class SessionResource extends Resource
{
    protected static ?string $model = Session::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';

    protected static ?string $navigationGroup = 'Quản lý chiến dịch';

    protected static ?int $navigationSort = 2;

    protected static ?string $modelLabel = 'Session';

    protected static ?string $pluralModelLabel = 'Sessions';public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('session_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('delivery_type')
                    ->required(),
                Forms\Components\TextInput::make('budget')
                    ->numeric(),
                Forms\Components\DateTimePicker::make('start_time')
                    ->label('Thời gian bắt đầu')
                    ->required()
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\DateTimePicker::make('end_time')
                    ->label('Thời gian kết thúc')
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\Select::make('campaign_id')
                    ->relationship('campaign', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('session_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('delivery_type'),
                Tables\Columns\TextColumn::make('budget')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_time')
                    ->label('Thời gian bắt đầu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('end_time')
                    ->label('Thời gian kết thúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('campaign.name')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListSessions::route('/'),
            'create' => Pages\CreateSession::route('/create'),
            'edit' => Pages\EditSession::route('/{record}/edit'),
        ];
    }
}
