<?php

namespace App\Filament\Resources;

use App\Filament\Resources\CampaignResource\Actions\AiAnalyzeAction;
use App\Filament\Resources\CampaignResource\Actions\AiOptimizeBudgetAction;
use App\Filament\Resources\CampaignResource\Actions\BulkRefreshAiAnalysisAction;
use App\Filament\Resources\CampaignResource\Actions\BulkSyncCampaignDetailsAction;
use App\Filament\Resources\CampaignResource\Actions\RefreshAiAnalysisAction;
use App\Filament\Resources\CampaignResource\Actions\RefreshAllAiAnalysisAction;
use App\Filament\Resources\CampaignResource\Actions\SyncAllCampaignDetailsAction;
use App\Filament\Resources\CampaignResource\Actions\SyncAllCampaignsAction;
use App\Filament\Resources\CampaignResource\Actions\SyncCampaignDetailsAction;
use App\Filament\Resources\CampaignResource\Actions\SyncSessionsAction;
use App\Filament\Resources\CampaignResource\Actions\ViewAiAnalysisSummaryAction;
use App\Filament\Resources\CampaignResource\Pages;
use App\Models\Campaign;
use App\Helpers\DateHelper;
use Exception;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-megaphone';

    protected static ?string $navigationGroup = 'Quản lý chiến dịch';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Chiến dịch';

    protected static ?string $pluralModelLabel = 'Chiến dịch';



    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên chiến dịch')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Select::make('shop_id')
                    ->label('Cửa hàng')
                    ->relationship('shop', 'name')
                    ->required()
                    ->searchable()
                    ->preload(),
                Forms\Components\TextInput::make('target_roi')
                    ->label('Target ROI (%)')
                    ->numeric()
                    ->step(0.01)
                    ->suffix('%'),
                Forms\Components\TextInput::make('budget')
                    ->label('Ngân sách tổng')
                    ->numeric()
                    ->step(0.01)
                    ->prefix('₫'),
                Forms\Components\TextInput::make('daily_budget')
                    ->label('Ngân sách hàng ngày')
                    ->numeric()
                    ->step(0.01)
                    ->prefix('₫'),
                Forms\Components\DateTimePicker::make('start_date')
                    ->label('Ngày bắt đầu')
                    ->required()
                    ->default(now())
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\DateTimePicker::make('end_date')
                    ->label('Ngày kết thúc')
                    ->displayFormat('d/m/Y H:i')
                    ->format('Y-m-d H:i:s'),
                Forms\Components\Select::make('status')
                    ->label('Trạng thái')
                    ->options([
                        'draft' => 'Bản nháp',
                        'active' => 'Đang chạy',
                        'paused' => 'Tạm dừng',
                        'completed' => 'Hoàn thành',
                        'cancelled' => 'Đã hủy',
                    ])
                    ->default('draft')
                    ->required(),
                Forms\Components\TextInput::make('campaign_id')
                    ->label('Campaign ID')
                    ->maxLength(255)
                    ->unique(ignoreRecord: true)
                    ->helperText('Để trống để tự động tạo'),
                Forms\Components\TextInput::make('advertiser_id')
                    ->label('Advertiser ID')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->striped()
            ->defaultSort('created_at', 'desc')
            ->paginated([10, 25, 50, 100])
            ->defaultPaginationPageOption(25)
            ->columns([
                Tables\Columns\TextColumn::make('campaign_id')
                    ->label('Campaign ID')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->copyable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Campaign Name')
                    ->searchable()
                    ->toggleable()
                    ->weight('medium')
                    ->wrap()
                    ->limit(50),
                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->size('lg')
                    ->color(fn(string $state): string => match ($state) {
                        'active' => 'success',
                        'paused' => 'warning',
                        'completed' => 'info',
                        'cancelled' => 'danger',
                        default => 'gray',
                    })
                    ->icon(fn(string $state): string => match ($state) {
                        'active' => 'heroicon-o-play',
                        'paused' => 'heroicon-o-pause',
                        'completed' => 'heroicon-o-check',
                        'cancelled' => 'heroicon-o-x-mark',
                        default => 'heroicon-o-question-mark',
                    })
                    ->toggleable(),
                Tables\Columns\TextColumn::make('target_roi')
                    ->label('Target ROI')
                    ->numeric()
                    ->suffix('%')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('budget')
                    ->label('Total Budget')
                    ->money('VND')
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\TextColumn::make('daily_budget')
                    ->label('Daily Budget')
                    ->money('VND')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                Tables\Columns\TextColumn::make('start_date')
                    ->label('Ngày bắt đầu')
                    ->date('d/m/Y')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure()),
                Tables\Columns\TextColumn::make('end_date')
                    ->label('Ngày kết thúc')
                    ->date('d/m/Y')
                    ->placeholder('Không có ngày kết thúc')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(fn($record) => $record->end_date ? DateHelper::formatVietnameseDate($record->end_date) : 'Không có ngày kết thúc'),
                Tables\Columns\TextColumn::make('advertiser_id')
                    ->label('Advertiser ID')
                    ->searchable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->copyable(),
                Tables\Columns\TextColumn::make('shop.name')
                    ->label('Shop')
                    ->searchable()
                    ->sortable()
                    ->toggleable(),
                Tables\Columns\ViewColumn::make('ai_score')
                    ->label('AI Score')
                    ->view('filament.tables.columns.ai-score')
                    ->sortable()
                    ->toggleable()
                    ->width('100px'),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('deleted_at')
                    ->label('Xóa lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->options([
                        'active' => 'Active',
                        'paused' => 'Paused',
                        'completed' => 'Completed',
                        'cancelled' => 'Cancelled',
                    ])
                    ->default('active'),

                Tables\Filters\SelectFilter::make('shop_id')
                    ->relationship('shop', 'name')
                    ->searchable()
                    ->preload(),

                Tables\Filters\Filter::make('has_ai_analysis')
                    ->label('Has AI Analysis')
                    ->query(fn($query) => $query->whereHas('aiAnalyses', fn($q) => $q->where('is_current', true)))
                    ->toggle(),

                Tables\Filters\Filter::make('needs_ai_update')
                    ->label('Needs AI Update')
                    ->query(fn($query) => $query->whereHas('aiAnalyses', fn($q) => $q->where('is_current', true)
                        ->where('analyzed_at', '<', now()->subHours(24))
                    ))
                    ->toggle(),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->color('primary'),

                ViewAiAnalysisSummaryAction::make(),

                Tables\Actions\ActionGroup::make([
                    RefreshAiAnalysisAction::make()
                        ->color('info'),
                    AiAnalyzeAction::make()
                        ->color('success'),
                    AiOptimizeBudgetAction::make()
                        ->color('warning'),
                ])
                    ->label('AI Actions')
                    ->icon('heroicon-o-cpu-chip')
                    ->color('info')
                    ->button(),

                Tables\Actions\ActionGroup::make([
                    SyncCampaignDetailsAction::make()
                        ->color('info'),
                    SyncSessionsAction::make()
                        ->color('gray'),
                ])
                    ->label('Sync')
                    ->icon('heroicon-o-arrow-path')
                    ->color('gray')
                    ->button(),
            ])
            ->headerActions([
                SyncAllCampaignsAction::make()
                    ->color('primary'),

                SyncAllCampaignDetailsAction::make()
                    ->color('info'),

                RefreshAllAiAnalysisAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    BulkSyncCampaignDetailsAction::make(),
                    BulkRefreshAiAnalysisAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaigns::route('/'),
            'create' => Pages\CreateCampaign::route('/create'),
            'edit' => Pages\EditCampaign::route('/{record}/edit'),
        ];
    }
}
