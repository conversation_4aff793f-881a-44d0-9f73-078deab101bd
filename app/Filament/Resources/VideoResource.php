<?php

namespace App\Filament\Resources;

use App\Filament\Resources\VideoResource\Actions\SyncVideosAction;
use App\Filament\Resources\VideoResource\Pages;
use App\Models\Video;
use App\Helpers\DateHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class VideoResource extends Resource
{
    protected static ?string $model = Video::class;

    protected static ?string $navigationIcon = 'heroicon-o-video-camera';

    protected static ?string $navigationGroup = 'Nội dung & Media';

    protected static ?int $navigationSort = 1;

    protected static ?string $modelLabel = 'Video';

    protected static ?string $pluralModelLabel = 'Videos';public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('video_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('title')
                    ->required()
                    ->maxLength(255),
                Forms\Components\Textarea::make('url')
                    ->columnSpanFull(),
                Forms\Components\TextInput::make('thumbnail')
                    ->maxLength(255),
                Forms\Components\TextInput::make('duration')
                    ->numeric(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\Toggle::make('is_custom_anchor')
                    ->required(),
                Forms\Components\Select::make('campaign_id')
                    ->relationship('campaign', 'name')
                    ->required(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->label('Thumbnail')
                    ->circular()
                    ->size(60)
                    ->defaultImageUrl('/images/video-placeholder.png'),

                Tables\Columns\TextColumn::make('video_id')
                    ->label('Video ID')
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('title')
                    ->label('Caption')
                    ->searchable()
                    ->limit(50)
                    ->tooltip(function ($record) {
                        return $record->title;
                    }),

                Tables\Columns\TextColumn::make('duration')
                    ->label('Duration')
                    ->formatStateUsing(fn ($state) => $state ? gmdate('H:i:s', $state) : '-')
                    ->sortable()
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('status')
                    ->label('Status')
                    ->colors([
                        'success' => 'active',
                        'warning' => 'processing',
                        'danger' => 'failed',
                        'secondary' => 'inactive',
                    ]),

                Tables\Columns\IconColumn::make('is_custom_anchor')
                    ->label('Custom Anchor')
                    ->boolean()
                    ->toggleable(),

                Tables\Columns\IconColumn::make('can_change_anchor')
                    ->label('Can Change Anchor')
                    ->boolean()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('identity_display_name')
                    ->label('Identity')
                    ->getStateUsing(fn ($record) => $record->getIdentityDisplayName())
                    ->toggleable(),

                Tables\Columns\BadgeColumn::make('identity_type')
                    ->label('Identity Type')
                    ->getStateUsing(fn ($record) => $record->getIdentityType())
                    ->colors([
                        'primary' => 'TT_USER',
                        'success' => 'BC_AUTH_TT',
                        'warning' => 'TTS_TT',
                        'secondary' => 'AUTH_CODE',
                    ])
                    ->toggleable(),

                Tables\Columns\TextColumn::make('video_resolution')
                    ->label('Resolution')
                    ->getStateUsing(fn ($record) => $record->getVideoResolution())
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('spu_count')
                    ->label('Products')
                    ->getStateUsing(fn ($record) => count($record->getSpuIds()))
                    ->toggleable(),

                Tables\Columns\TextColumn::make('campaign.name')
                    ->label('Campaign')
                    ->sortable()
                    ->searchable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),

                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Status')
                    ->options([
                        'active' => 'Active',
                        'inactive' => 'Inactive',
                        'processing' => 'Processing',
                        'failed' => 'Failed',
                    ])
                    ->default('active'),

                Tables\Filters\TernaryFilter::make('is_custom_anchor')
                    ->label('Custom Anchor')
                    ->placeholder('All videos')
                    ->trueLabel('Custom anchor only')
                    ->falseLabel('Non-custom anchor only'),

                Tables\Filters\TernaryFilter::make('can_change_anchor')
                    ->label('Can Change Anchor')
                    ->placeholder('All videos')
                    ->trueLabel('Can change anchor')
                    ->falseLabel('Cannot change anchor'),

                Tables\Filters\SelectFilter::make('identity_type')
                    ->label('Identity Type')
                    ->options([
                        'TT_USER' => 'TikTok User',
                        'BC_AUTH_TT' => 'Business Center Auth',
                        'TTS_TT' => 'TikTok Shop',
                        'AUTH_CODE' => 'Auth Code',
                    ]),

                Tables\Filters\SelectFilter::make('campaign')
                    ->relationship('campaign', 'name')
                    ->label('Campaign')
                    ->searchable()
                    ->preload(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                SyncVideosAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListVideos::route('/'),
            'create' => Pages\CreateVideo::route('/create'),
            'edit' => Pages\EditVideo::route('/{record}/edit'),
        ];
    }
}
