<?php

namespace App\Filament\Resources\CampaignResource\Actions;

use App\Helpers\ErrorHandler;
use App\Models\Campaign;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\VideoSyncService;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables\Actions\Action;

class GetCampaignVideosAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'get_campaign_videos';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this->label('Get Videos')
            ->icon('heroicon-o-video-camera')
            ->color('info')
            ->modalHeading('Get Videos for Campaign')
            ->modalDescription('Retrieve and sync TikTok videos available for this Product GMV Max campaign.')
            ->modalWidth(MaxWidth::FourExtraLarge)
            ->form($this->getFormSchema())
            ->action(function (Campaign $record, array $data) {
                $this->handleGetVideos($record, $data);
            });
    }

    protected function getFormSchema(): array
    {
        return [
            Section::make('Video Retrieval Options')
                ->description('Configure options for retrieving videos from TikTok API')
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('keyword')
                                ->label('Search Keyword')
                                ->placeholder('Enter post ID or caption text')
                                ->helperText('Search by post caption (text) or post ID (19+ digit number)')
                                ->maxLength(255),

                            Select::make('sort_field')
                                ->label('Sort Field')
                                ->options([
                                    'GMV' => 'GMV',
                                    'POST_TIME' => 'Post Time',
                                    'VIDEO_VIEWS' => 'Video Views',
                                    'VIDEO_LIKES' => 'Video Likes',
                                    'CLICK_THROUGH_RATE' => 'Click-through Rate',
                                    'PRODUCT_CLICKS' => 'Product Clicks',
                                ])
                                ->default('GMV')
                                ->helperText('Only valid when custom posts is disabled'),

                            Select::make('sort_type')
                                ->label('Sort Order')
                                ->options([
                                    'DESC' => 'Descending',
                                    'ASC' => 'Ascending',
                                ])
                                ->default('DESC'),

                            TextInput::make('page_size')
                                ->label('Page Size')
                                ->numeric()
                                ->default(20)
                                ->minValue(1)
                                ->maxValue(50)
                                ->helperText('Number of videos per page (1-50)'),
                        ]),

                    Grid::make(1)
                        ->schema([
                            TagsInput::make('spu_id_list')
                                ->label('Product SPU IDs')
                                ->placeholder('Enter SPU IDs to filter by specific products')
                                ->helperText('Leave empty to get videos for all products. Max 50 items (or 1 if custom posts enabled)')
                                ->separator(','),
                        ]),

                    Grid::make(2)
                        ->schema([
                            Toggle::make('custom_posts_eligible')
                                ->label('Custom Posts Eligible')
                                ->helperText('Query videos for customized posts (requires exactly 1 SPU ID)')
                                ->reactive(),

                            Toggle::make('need_auth_code_video')
                                ->label('Include AUTH_CODE Videos')
                                ->helperText('Include posts associated with AUTH_CODE identities')
                                ->default(false),
                        ]),
                ]),

            Section::make('Advanced Options')
                ->description('Additional configuration options')
                ->collapsed()
                ->schema([
                    Grid::make(2)
                        ->schema([
                            TextInput::make('max_pages')
                                ->label('Max Pages to Fetch')
                                ->numeric()
                                ->default(5)
                                ->minValue(1)
                                ->maxValue(20)
                                ->helperText('Maximum number of pages to retrieve (1-20)'),

                            Toggle::make('sync_to_database')
                                ->label('Sync to Database')
                                ->default(true)
                                ->helperText('Save retrieved videos to database'),
                        ]),
                ]),
        ];
    }

    protected function handleGetVideos(Campaign $record, array $data): void
    {
        try {
            // Validate campaign has required data
            if (!$record->store_id || !$record->store_authorized_bc_id) {
                Notification::make()
                    ->title('Campaign Configuration Error')
                    ->body('Campaign is missing required store information. Please sync campaign details first.')
                    ->danger()
                    ->send();
                return;
            }

            // Validate custom posts eligibility
            if ($data['custom_posts_eligible'] ?? false) {
                $spuIds = $data['spu_id_list'] ?? [];
                if (count($spuIds) !== 1) {
                    Notification::make()
                        ->title('Validation Error')
                        ->body('When custom posts is enabled, you must specify exactly 1 SPU ID.')
                        ->danger()
                        ->send();
                    return;
                }
            }

            // Initialize services
            $apiService = new TikTokApiService();
            if (!$apiService->isConfigured()) {
                $error = $apiService->getLastError();
                Notification::make()
                    ->title('TikTok API Configuration Error')
                    ->body($error['message'] ?? 'TikTok API is not properly configured')
                    ->danger()
                    ->send();
                return;
            }

            // Build API options
            $apiOptions = [
                'store_id' => $record->store_id,
                'store_authorized_bc_id' => $record->store_authorized_bc_id,
                'page_size' => min(max(1, (int)($data['page_size'] ?? 20)), 50),
            ];

            // Add optional parameters
            if (!empty($data['keyword'])) {
                $apiOptions['keyword'] = trim($data['keyword']);
            }

            if (!empty($data['spu_id_list'])) {
                $apiOptions['spu_id_list'] = array_filter($data['spu_id_list']);
            }

            if (isset($data['custom_posts_eligible'])) {
                $apiOptions['custom_posts_eligible'] = (bool)$data['custom_posts_eligible'];
            }

            if (isset($data['need_auth_code_video'])) {
                $apiOptions['need_auth_code_video'] = (bool)$data['need_auth_code_video'];
            }

            // Add sorting (only if custom posts is disabled)
            if (!($data['custom_posts_eligible'] ?? false)) {
                if (!empty($data['sort_field'])) {
                    $apiOptions['sort_field'] = $data['sort_field'];
                }
            }

            if (!empty($data['sort_type'])) {
                $apiOptions['sort_type'] = $data['sort_type'];
            }

            // Add identity list if available in campaign
            if ($record->identity_list && is_array($record->identity_list)) {
                $apiOptions['identity_list'] = $record->identity_list;
            }

            // Get videos from API
            $response = $apiService->getVideos($apiOptions);

            if (!ErrorHandler::isSuccess($response)) {
                $errorMessage = $response['message'] ?? 'Failed to retrieve videos';
                Notification::make()
                    ->title('API Error')
                    ->body($errorMessage)
                    ->danger()
                    ->send();
                return;
            }

            $responseData = $response['data']['data'] ?? [];
            $videos = $responseData['item_list'] ?? [];
            $pageInfo = $responseData['page_info'] ?? [];

            // Sync to database if requested
            if ($data['sync_to_database'] ?? true) {
                $videoSyncService = new VideoSyncService($apiService);
                $syncResult = $videoSyncService->syncVideosForCampaign($record, $apiOptions);

                if (ErrorHandler::isSuccess($syncResult)) {
                    $stats = $syncResult['data']['stats'] ?? [];
                    $message = sprintf(
                        'Successfully retrieved %d videos (%d created, %d updated)',
                        $stats['total'] ?? 0,
                        $stats['created'] ?? 0,
                        $stats['updated'] ?? 0
                    );
                } else {
                    $message = 'Videos retrieved but sync failed: ' . ($syncResult['message'] ?? 'Unknown error');
                }
            } else {
                $message = sprintf('Successfully retrieved %d videos (not synced to database)', count($videos));
            }

            // Show pagination info if available
            if (!empty($pageInfo)) {
                $paginationInfo = sprintf(
                    ' | Page %d of %d (Total: %d)',
                    $pageInfo['page'] ?? 1,
                    $pageInfo['total_page'] ?? 1,
                    $pageInfo['total_number'] ?? count($videos)
                );
                $message .= $paginationInfo;
            }

            Notification::make()
                ->title('Videos Retrieved Successfully')
                ->body($message)
                ->success()
                ->duration(8000)
                ->send();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Video Retrieval Error')
                ->body('An unexpected error occurred: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }
}
