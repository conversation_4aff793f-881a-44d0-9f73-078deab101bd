<?php

namespace App\Filament\Resources;

use App\Filament\Resources\IdentityResource\Pages;
use App\Filament\Resources\IdentityResource\RelationManagers;
use App\Models\Identity;
use App\Services\TikTok\TikTokApiService;
use App\Services\TikTok\TikTokSyncService;
use App\Helpers\DateHelper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Table;

class IdentityResource extends Resource
{
    protected static ?string $model = Identity::class;

    protected static ?string $navigationIcon = 'heroicon-o-identification';

    protected static ?string $navigationGroup = 'Quyền & Ủy quyền';

    protected static ?int $navigationSort = 3;

    protected static ?string $modelLabel = 'Identity GMV Max';

    protected static ?string $pluralModelLabel = 'Identities GMV Max';public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('identity_id')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),
                Forms\Components\TextInput::make('type')
                    ->required(),
                Forms\Components\TextInput::make('status')
                    ->required(),
                Forms\Components\TextInput::make('advertiser_id')
                    ->required()
                    ->maxLength(255),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('identity_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('name')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type'),
                Tables\Columns\TextColumn::make('status'),
                Tables\Columns\TextColumn::make('advertiser_id')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
                Tables\Columns\TextColumn::make('updated_at')
                    ->label('Cập nhật lúc')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true)
                    ->tooltip(DateHelper::tooltipClosure('d/m/Y lúc H:i:s')),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->headerActions([
                Action::make('sync_identities')
                    ->label('Sync Identities')
                    ->icon('heroicon-o-cloud-arrow-down')
                    ->color('success')
                    ->action(function () {
                        $apiService = new TikTokApiService();
                        $syncService = new TikTokSyncService($apiService);

                        $result = $syncService->syncIdentities();

                        if ($result['success']) {
                            Notification::make()
                                ->title('Identities synced successfully')
                                ->body("Synced {$result['synced']} identities from TikTok API")
                                ->success()
                                ->send();
                        } else {
                            Notification::make()
                                ->title('Sync failed')
                                ->body($result['error'])
                                ->danger()
                                ->send();
                        }
                    })
                    ->requiresConfirmation()
                    ->modalHeading('Sync Identities from TikTok API')
                    ->modalDescription('This will fetch and sync all GMV Max identities from TikTok API.'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListIdentities::route('/'),
            'create' => Pages\CreateIdentity::route('/create'),
            'edit' => Pages\EditIdentity::route('/{record}/edit'),
        ];
    }
}
