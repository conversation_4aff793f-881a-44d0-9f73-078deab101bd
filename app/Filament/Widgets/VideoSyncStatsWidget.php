<?php

namespace App\Filament\Widgets;

use App\Models\Video;
use App\Models\Campaign;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\DB;

class VideoSyncStatsWidget extends BaseWidget
{
    protected static ?string $pollingInterval = '30s';
    protected static bool $isLazy = false;

    protected function getStats(): array
    {
        return [
            $this->getTotalVideosStats(),
            $this->getRecentSyncStats(),
            $this->getCustomAnchorStats(),
            $this->getCampaignsWithVideosStats(),
        ];
    }

    private function getTotalVideosStats(): Stat
    {
        $totalVideos = Video::count();
        $activeVideos = Video::where('status', 'active')->count();
        
        $percentageActive = $totalVideos > 0 ? round(($activeVideos / $totalVideos) * 100, 1) : 0;

        return Stat::make('Total Videos', number_format($totalVideos))
            ->description("{$activeVideos} active ({$percentageActive}%)")
            ->descriptionIcon('heroicon-m-video-camera')
            ->color('primary')
            ->chart($this->getVideoTrendChart());
    }

    private function getRecentSyncStats(): Stat
    {
        $recentVideos = Video::where('created_at', '>=', now()->subDays(7))->count();
        $todayVideos = Video::where('created_at', '>=', now()->startOfDay())->count();
        
        $trend = $this->calculateTrend('videos', 7);
        $trendIcon = $trend >= 0 ? 'heroicon-m-arrow-trending-up' : 'heroicon-m-arrow-trending-down';
        $trendColor = $trend >= 0 ? 'success' : 'danger';

        return Stat::make('Recent Syncs (7 days)', number_format($recentVideos))
            ->description("Today: {$todayVideos} | Trend: " . ($trend >= 0 ? '+' : '') . "{$trend}%")
            ->descriptionIcon($trendIcon)
            ->color($trendColor)
            ->chart($this->getRecentSyncChart());
    }

    private function getCustomAnchorStats(): Stat
    {
        $customAnchorVideos = Video::where('can_change_anchor', true)->count();
        $totalVideos = Video::count();
        
        $percentage = $totalVideos > 0 ? round(($customAnchorVideos / $totalVideos) * 100, 1) : 0;

        return Stat::make('Custom Anchor Videos', number_format($customAnchorVideos))
            ->description("{$percentage}% of total videos")
            ->descriptionIcon('heroicon-m-link')
            ->color('warning');
    }

    private function getCampaignsWithVideosStats(): Stat
    {
        $campaignsWithVideos = Campaign::has('videos')->count();
        $totalActiveCampaigns = Campaign::where('status', 'active')->count();
        
        $percentage = $totalActiveCampaigns > 0 ? round(($campaignsWithVideos / $totalActiveCampaigns) * 100, 1) : 0;

        return Stat::make('Campaigns with Videos', number_format($campaignsWithVideos))
            ->description("{$percentage}% of active campaigns")
            ->descriptionIcon('heroicon-m-film')
            ->color('info');
    }

    private function getVideoTrendChart(): array
    {
        return Video::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function getRecentSyncChart(): array
    {
        return Video::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->where('created_at', '>=', now()->subDays(7))
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count')
            ->toArray();
    }

    private function calculateTrend(string $metric, int $days): float
    {
        $currentPeriod = Video::where('created_at', '>=', now()->subDays($days))->count();
        $previousPeriod = Video::where('created_at', '>=', now()->subDays($days * 2))
            ->where('created_at', '<', now()->subDays($days))
            ->count();

        if ($previousPeriod == 0) {
            return $currentPeriod > 0 ? 100 : 0;
        }

        return round((($currentPeriod - $previousPeriod) / $previousPeriod) * 100, 1);
    }

    public function getDisplayName(): string
    {
        return 'Video Sync Statistics';
    }
}
