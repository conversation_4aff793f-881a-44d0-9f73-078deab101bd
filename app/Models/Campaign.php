<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model Campaign - Quản lý chiến dịch GMV Max
 *
 * @property string $campaign_id Campaign ID từ TikTok API
 * @property string $name Tên chiến dịch
 * @property string $status Trạng thái chiến dịch
 * @property float $target_roi Target ROI (%)
 * @property float $budget Ngân sách tổng
 * @property float $daily_budget Ngân sách hàng ngày
 * @property Carbon $start_date Ngày bắt đầu
 * @property Carbon $end_date Ngày kết thúc
 * @property string $advertiser_id Advertiser ID
 * @property int $shop_id ID cửa hàng
 */
class Campaign extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Tên bảng trong database
     */
    protected $table = 'gmv_max_campaigns';

    /**
     * <PERSON><PERSON><PERSON> trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'campaign_id',
        'name',
        'status',
        'operation_status',
        'primary_status',
        'secondary_status',
        'roi_protection_status',
        'objective_type',
        'target_roi',
        'budget',
        'daily_budget',
        'start_date',
        'end_date',
        'advertiser_id',
        'shop_id',
        // Campaign details fields
        'store_id',
        'store_authorized_bc_id',
        'shopping_ads_type',
        'product_specific_type',
        'optimization_goal',
        'roi_protection_enabled',
        'deep_bid_type',
        'roas_bid',
        'budget_details',
        'schedule_type',
        'schedule_start_time',
        'schedule_end_time',
        'placements',
        'location_ids',
        'age_groups',
        'product_video_specific_type',
        'affiliate_posts_enabled',
        'item_group_ids',
        'identity_list',
        'item_list',
        'custom_anchor_video_list',
        'campaign_custom_anchor_video_id',
        'details_synced_at',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu theo JSON specification
     */
    protected $casts = [
        'target_roi' => 'decimal:2',
        'budget' => 'decimal:2',
        'daily_budget' => 'decimal:2',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        // Campaign details casts
        'roi_protection_enabled' => 'boolean',
        'roas_bid' => 'decimal:2',
        'budget_details' => 'decimal:2',
        'schedule_start_time' => 'datetime',
        'schedule_end_time' => 'datetime',
        'placements' => 'array',
        'location_ids' => 'array',
        'age_groups' => 'array',
        'affiliate_posts_enabled' => 'boolean',
        'item_group_ids' => 'array',
        'identity_list' => 'array',
        'item_list' => 'array',
        'custom_anchor_video_list' => 'array',
        'details_synced_at' => 'datetime',
    ];

    /**
     * Relationship: Campaign thuộc về một Shop
     */
    public function shop(): BelongsTo
    {
        return $this->belongsTo(Shop::class);
    }

    /**
     * Relationship: Campaign có nhiều Sessions
     */
    public function sessions(): HasMany
    {
        return $this->hasMany(Session::class);
    }

    /**
     * Relationship: Campaign có nhiều Reports
     * Map campaign_id (TikTok Campaign ID) với campaign_id field trong campaign_reports
     */
    public function reports(): HasMany
    {
        return $this->hasMany(CampaignReport::class, 'campaign_id', 'campaign_id');
    }

    /**
     * Relationship: Campaign có nhiều AI Analyses
     */
    public function aiAnalyses(): HasMany
    {
        return $this->hasMany(AiAnalysis::class);
    }

    public function currentAiAnalyses(): HasMany
    {
        return $this->hasMany(AiAnalysis::class)->where('is_current', true);
    }

    public function getCurrentAiAnalysis(string $type)
    {
        return $this->aiAnalyses()
            ->where('analysis_type', $type)
            ->where('is_current', true)
            ->first();
    }

    /**
     * Relationship: Campaign có nhiều Videos
     */
    public function videos(): HasMany
    {
        return $this->hasMany(Video::class);
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Chỉ lấy campaigns active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Lọc campaigns đang chạy (active và trong thời gian)
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'active')
            ->where('start_date', '<=', now())
            ->where(function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });
    }

    /**
     * Scope: Lọc theo shop
     */
    public function scopeByShop($query, $shopId)
    {
        return $query->where('shop_id', $shopId);
    }

    /**
     * Accessor: Kiểm tra campaign có đang chạy không
     */
    public function getIsRunningAttribute(): bool
    {
        return $this->status === 'active'
            && $this->start_date <= now()
            && ($this->end_date === null || $this->end_date >= now());
    }

    /**
     * Accessor: Tính số ngày còn lại
     */
    public function getDaysRemainingAttribute(): ?int
    {
        if (!$this->end_date) {
            return null;
        }

        return max(0, now()->diffInDays($this->end_date, false));
    }

    /**
     * Business method: Tính tổng chi phí từ reports
     */
    public function getTotalCost(): float
    {
        return $this->reports()->sum('total_cost');
    }

    /**
     * Business method: Tính tổng doanh thu từ reports
     */
    public function getTotalRevenue(): float
    {
        return $this->reports()->sum('gross_revenue');
    }

    /**
     * Business method: Tính ROI thực tế
     */
    public function getActualRoi(): float
    {
        $totalCost = $this->getTotalCost();
        $totalRevenue = $this->getTotalRevenue();

        if ($totalCost == 0) {
            return 0;
        }

        return (($totalRevenue - $totalCost) / $totalCost) * 100;
    }

    /**
     * Scope: Lọc theo operation status
     */
    public function scopeByOperationStatus($query, $operationStatus)
    {
        return $query->where('operation_status', $operationStatus);
    }

    /**
     * Scope: Lọc theo primary status
     */
    public function scopeByPrimaryStatus($query, $primaryStatus)
    {
        return $query->where('primary_status', $primaryStatus);
    }

    /**
     * Scope: Lọc theo ROI protection status
     */
    public function scopeByRoiProtectionStatus($query, $roiStatus)
    {
        return $query->where('roi_protection_status', $roiStatus);
    }

    /**
     * Scope: Chỉ lấy campaigns có ROI protection
     */
    public function scopeWithRoiProtection($query)
    {
        return $query->where('roi_protection_status', 'IN_EFFECT');
    }

    /**
     * Scope: Lọc campaigns theo objective type
     */
    public function scopeByObjectiveType($query, $objectiveType)
    {
        return $query->where('objective_type', $objectiveType);
    }

    /**
     * Accessor: Kiểm tra campaign có ROI protection không
     */
    public function getHasRoiProtectionAttribute(): bool
    {
        return $this->roi_protection_status === 'IN_EFFECT';
    }

    /**
     * Accessor: Lấy trạng thái hiển thị cho user
     */
    public function getDisplayStatusAttribute(): string
    {
        $statusMap = [
            'active' => 'Đang chạy',
            'inactive' => 'Tạm dừng',
            'deleted' => 'Đã xóa',
            'draft' => 'Bản nháp',
            'paused' => 'Tạm dừng',
            'completed' => 'Hoàn thành',
            'cancelled' => 'Đã hủy'
        ];

        return $statusMap[$this->status] ?? 'Không xác định';
    }

    /**
     * Accessor: Lấy ROI protection status hiển thị
     */
    public function getRoiProtectionDisplayAttribute(): string
    {
        $statusMap = [
            'IN_EFFECT' => 'Có hiệu lực',
            'NOT_ELIGIBLE' => 'Không đủ điều kiện'
        ];

        return $statusMap[$this->roi_protection_status] ?? 'Chưa xác định';
    }

    /**
     * Business method: Kiểm tra campaign có đang được bảo vệ ROI không
     */
    public function isRoiProtected(): bool
    {
        return $this->roi_protection_status === 'IN_EFFECT' && $this->status === 'active';
    }

    /**
     * Business method: Lấy thông tin tổng quan campaign
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'campaign_id' => $this->campaign_id,
            'name' => $this->name,
            'status' => $this->status,
            'display_status' => $this->display_status,
            'operation_status' => $this->operation_status,
            'primary_status' => $this->primary_status,
            'roi_protection' => $this->has_roi_protection,
            'roi_protection_display' => $this->roi_protection_display,
            'is_running' => $this->is_running,
            'days_remaining' => $this->days_remaining,
            'total_cost' => $this->getTotalCost(),
            'total_revenue' => $this->getTotalRevenue(),
            'actual_roi' => $this->getActualRoi(),
            'target_roi' => $this->target_roi,
            'shop_name' => $this->shop->name ?? null,
        ];
    }
}
