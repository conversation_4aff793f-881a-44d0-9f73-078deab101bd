<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

/**
 * Model Session - Quản lý sessions trong campaign
 *
 * @property string $session_id Session ID từ TikTok API
 * @property int $campaign_id ID chiến dịch
 * @property string $name Tên session
 * @property string $status Trạng thái session
 * @property string $delivery_type Loại triển khai
 * @property float $budget Ngân sách session
 * @property Carbon $start_time Thời gian bắt đầu
 * @property Carbon $end_time Thời gian kết thúc
 */
class Session extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'gmv_max_sessions';

    /**
     * Các trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'session_id',
        'campaign_id',
        'name',
        'status',
        'delivery_type',
        'budget',
        'start_time',
        'end_time',
    ];

    /**
     * <PERSON><PERSON><PERSON> trư<PERSON> cần cast kiểu dữ liệu theo JSON specification
     */
    protected $casts = [
        'budget' => 'decimal:2',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: Session thuộc về một Campaign
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id', 'campaign_id');
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Chỉ lấy sessions active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Lọc sessions đang chạy
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'active')
                    ->where('start_time', '<=', now())
                    ->where(function($q) {
                        $q->whereNull('end_time')
                          ->orWhere('end_time', '>=', now());
                    });
    }

    /**
     * Scope: Lọc theo loại delivery
     */
    public function scopeByDeliveryType($query, $deliveryType)
    {
        return $query->where('delivery_type', $deliveryType);
    }

    /**
     * Accessor: Kiểm tra session có đang chạy không
     */
    public function getIsRunningAttribute(): bool
    {
        return $this->status === 'active'
               && $this->start_time <= now()
               && ($this->end_time === null || $this->end_time >= now());
    }

    /**
     * Accessor: Tính thời gian còn lại (phút)
     */
    public function getMinutesRemainingAttribute(): ?int
    {
        if (!$this->end_time) {
            return null;
        }

        return max(0, now()->diffInMinutes($this->end_time, false));
    }

    /**
     * Business method: Kiểm tra session có thể start không
     */
    public function canStart(): bool
    {
        return in_array($this->status, ['draft', 'paused'])
               && $this->campaign->status === 'active';
    }

    /**
     * Business method: Kiểm tra session có thể pause không
     */
    public function canPause(): bool
    {
        return $this->status === 'active';
    }

    /**
     * Business method: Tính duration của session (phút)
     */
    public function getDurationInMinutes(): ?int
    {
        if (!$this->start_time || !$this->end_time) {
            return null;
        }

        return $this->start_time->diffInMinutes($this->end_time);
    }
}
