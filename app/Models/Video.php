<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

/**
 * Model Video - Quản lý video content cho campaigns
 *
 * @property string $video_id Video ID từ TikTok API
 * @property int $campaign_id ID chiến dịch
 * @property string $title Tiêu đề video
 * @property string $url URL video
 * @property string $thumbnail Đường dẫn thumbnail
 * @property int $duration Thời lượng video (giây)
 * @property string $status Trạng thái video
 * @property bool $is_custom_anchor Là custom anchor video
 */
class Video extends Model
{
    use HasFactory;

    /**
     * Tên bảng trong database
     */
    protected $table = 'gmv_max_videos';

    /**
     * Các trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'video_id',
        'campaign_id',
        'title',
        'url',
        'thumbnail',
        'duration',
        'status',
        'is_custom_anchor',
        'spu_id_list',
        'identity_info',
        'video_info',
        'can_change_anchor',
    ];

    /**
     * <PERSON><PERSON><PERSON> trườ<PERSON> cần cast kiểu dữ liệu
     */
    protected $casts = [
        'duration' => 'integer',
        'is_custom_anchor' => 'boolean',
        'can_change_anchor' => 'boolean',
        'spu_id_list' => 'array',
        'identity_info' => 'array',
        'video_info' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Relationship: Video thuộc về một Campaign
     */
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, 'campaign_id', 'campaign_id');
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Chỉ lấy videos active
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Lọc custom anchor videos
     */
    public function scopeCustomAnchor($query)
    {
        return $query->where('is_custom_anchor', true);
    }

    /**
     * Scope: Lọc videos có thể thay đổi anchor
     */
    public function scopeCanChangeAnchor($query)
    {
        return $query->where('can_change_anchor', true);
    }

    /**
     * Scope: Lọc videos theo identity type
     */
    public function scopeByIdentityType($query, string $identityType)
    {
        return $query->whereJsonContains('identity_info->identity_type', $identityType);
    }

    /**
     * Scope: Lọc videos có chứa sản phẩm cụ thể
     */
    public function scopeWithProduct($query, string $spuId)
    {
        return $query->whereJsonContains('spu_id_list', $spuId);
    }

    /**
     * Scope: Lọc theo campaign
     */
    public function scopeByCampaign($query, $campaignId)
    {
        return $query->where('campaign_id', $campaignId);
    }

    /**
     * Scope: Lọc theo duration range
     */
    public function scopeByDurationRange($query, $minDuration, $maxDuration)
    {
        return $query->whereBetween('duration', [$minDuration, $maxDuration]);
    }

    /**
     * Accessor: Format duration thành mm:ss
     */
    public function getFormattedDurationAttribute(): string
    {
        if (!$this->duration) {
            return '00:00';
        }

        $minutes = floor($this->duration / 60);
        $seconds = $this->duration % 60;

        return sprintf('%02d:%02d', $minutes, $seconds);
    }

    /**
     * Accessor: Lấy thumbnail URL đầy đủ
     */
    public function getThumbnailUrlAttribute(): ?string
    {
        if (!$this->thumbnail) {
            return null;
        }

        // Nếu là URL đầy đủ thì return luôn
        if (filter_var($this->thumbnail, FILTER_VALIDATE_URL)) {
            return $this->thumbnail;
        }

        // Nếu là path local thì tạo URL từ storage
        return Storage::url($this->thumbnail);
    }

    /**
     * Business method: Kiểm tra video có thể sử dụng cho campaign không
     */
    public function canBeUsedForCampaign(): bool
    {
        return $this->status === 'active' && $this->campaign->status !== 'cancelled';
    }

    /**
     * Business method: Kiểm tra video có phải short video không (< 60s)
     */
    public function isShortVideo(): bool
    {
        return $this->duration && $this->duration < 60;
    }

    /**
     * Business method: Kiểm tra video có phải long video không (>= 60s)
     */
    public function isLongVideo(): bool
    {
        return $this->duration && $this->duration >= 60;
    }

    /**
     * Business method: Cập nhật trạng thái video
     */
    public function updateStatus(string $status): bool
    {
        $validStatuses = ['active', 'inactive', 'processing', 'failed'];

        if (!in_array($status, $validStatuses)) {
            return false;
        }

        return $this->update(['status' => $status]);
    }

    /**
     * Business method: Xóa thumbnail file khi xóa video
     */
    public function deleteThumbnailFile(): bool
    {
        if (!$this->thumbnail || filter_var($this->thumbnail, FILTER_VALIDATE_URL)) {
            return true; // Không cần xóa nếu là URL external
        }

        return Storage::delete($this->thumbnail);
    }

    /**
     * Business method: Lấy thông tin identity display name
     */
    public function getIdentityDisplayName(): ?string
    {
        return $this->identity_info['display_name'] ?? null;
    }

    /**
     * Business method: Lấy identity type
     */
    public function getIdentityType(): ?string
    {
        return $this->identity_info['identity_type'] ?? null;
    }

    /**
     * Business method: Kiểm tra video có thể dùng cho customized posts không
     */
    public function canUseForCustomizedPosts(): bool
    {
        return $this->can_change_anchor === true;
    }

    /**
     * Business method: Lấy danh sách SPU IDs
     */
    public function getSpuIds(): array
    {
        return $this->spu_id_list ?? [];
    }

    /**
     * Business method: Kiểm tra video có chứa sản phẩm cụ thể không
     */
    public function hasProduct(string $spuId): bool
    {
        return in_array($spuId, $this->getSpuIds());
    }

    /**
     * Business method: Lấy video resolution từ video_info
     */
    public function getVideoResolution(): ?string
    {
        $videoInfo = $this->video_info ?? [];
        $width = $videoInfo['width'] ?? null;
        $height = $videoInfo['height'] ?? null;

        if ($width && $height) {
            return "{$width}x{$height}";
        }

        return $videoInfo['definition'] ?? null;
    }

    /**
     * Business method: Lấy video file size
     */
    public function getVideoSize(): ?int
    {
        return $this->video_info['size'] ?? null;
    }

    /**
     * Business method: Lấy video format
     */
    public function getVideoFormat(): ?string
    {
        return $this->video_info['format'] ?? null;
    }

    /**
     * Business method: Lấy video FPS
     */
    public function getVideoFps(): ?int
    {
        return $this->video_info['fps'] ?? null;
    }

    /**
     * Boot method để handle events
     */
    protected static function boot()
    {
        parent::boot();

        // Xóa thumbnail file khi xóa video
        static::deleting(function ($video) {
            $video->deleteThumbnailFile();
        });
    }
}
