<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdvertiserAccount extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Tên bảng trong database
     */
    protected $table = 'advertiser_accounts';

    /**
     * Các trường có thể mass assignment
     */
    protected $fillable = [
        'advertiser_id',
        'advertiser_name',
        'status',
        'currency',
        'timezone',
        'company_name',
        'industry',
        'permissions',
        'balance',
        'authorized_at',
        'last_sync_at',
    ];

    /**
     * <PERSON><PERSON>c trường cần cast kiểu dữ liệu
     */
    protected $casts = [
        'permissions' => 'array',
        'balance' => 'decimal:2',
        'authorized_at' => 'datetime',
        'last_sync_at' => 'datetime',
    ];

    /**
     * Relationship: Một advertiser account có nhiều shops
     * Sử dụng advertiser_account_id foreign key
     */
    public function shops(): HasMany
    {
        return $this->hasMany(Shop::class, 'advertiser_account_id', 'id');
    }

    /**
     * Alternative relationship: Shops liên kết qua advertiser_id
     * Dùng khi cần tìm shops dựa trên advertiser_id từ TikTok API
     */
    public function shopsByAdvertiserId(): HasMany
    {
        return $this->hasMany(Shop::class, 'advertiser_id', 'advertiser_id');
    }

    /**
     * Relationship: Một advertiser account có nhiều campaigns thông qua shops
     */
    public function campaigns(): hasManyThrough
    {
        return $this->hasManyThrough(Campaign::class, Shop::class);
    }

    /**
     * Scope: Chỉ lấy các advertiser accounts đang hoạt động
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope: Lấy theo currency
     */
    public function scopeByCurrency($query, $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Accessor: Format balance với currency
     */
    public function getFormattedBalanceAttribute(): string
    {
        $currencySymbols = [
            'VND' => '₫',
            'USD' => '$',
            'THB' => '฿',
        ];

        $symbol = $currencySymbols[$this->currency] ?? $this->currency;

        if ($this->currency === 'VND') {
            return $symbol . number_format($this->balance, 0, ',', '.');
        }

        return $symbol . number_format($this->balance, 2);
    }

    /**
     * Accessor: Kiểm tra xem account có được authorize gần đây không
     */
    public function getIsRecentlyAuthorizedAttribute(): bool
    {
        if (!$this->authorized_at) {
            return false;
        }

        return $this->authorized_at->diffInDays(now()) <= 30;
    }

    /**
     * Accessor: Kiểm tra xem account có cần sync không
     */
    public function getNeedsSyncAttribute(): bool
    {
        if (!$this->last_sync_at) {
            return true;
        }

        return $this->last_sync_at->diffInHours(now()) >= 6;
    }

    /**
     * Method: Cập nhật thời gian sync cuối cùng
     */
    public function markAsSynced(): void
    {
        $this->update(['last_sync_at' => now()]);
    }

    /**
     * Method: Tính tổng số campaigns đang hoạt động
     */
    public function getActiveCampaignsCount(): int
    {
        return $this->campaigns()->where('status', 'active')->count();
    }

    /**
     * Method: Tính tổng doanh thu từ tất cả campaigns
     */
    public function getTotalRevenue(): float
    {
        return $this->campaigns()
            ->join('campaign_reports', 'gmv_max_campaigns.id', '=', 'campaign_reports.campaign_id')
            ->sum('campaign_reports.gross_revenue');
    }

    /**
     * Method: Tính ROI trung bình
     */
    public function getAverageROI(): float
    {
        return $this->campaigns()
            ->join('campaign_reports', 'gmv_max_campaigns.id', '=', 'campaign_reports.campaign_id')
            ->avg('campaign_reports.roi') ?? 0;
    }
}
