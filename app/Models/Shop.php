<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Model Shop - Quản lý cửa hàng TikTok
 *
 * @property string $shop_id TikTok Shop ID từ API
 * @property string $name Tên cửa hàng
 * @property string $status Trạng thái cửa hàng
 * @property bool $is_eligible_gmv_max Đủ điều kiện GMV Max
 * @property string $region Khu vực
 * @property string $currency Tiền tệ
 * @property string $advertiser_id Advertiser ID từ TikTok
 * @property string $exclusive_authorization_status Trạng thái ủy quyền độc quyền
 */
class Shop extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Tên bảng trong database
     */
    protected $table = 'tiktok_shops';

    /**
     * <PERSON><PERSON><PERSON> trường có thể mass assignment theo JSON specification
     */
    protected $fillable = [
        'shop_id',
        'name',
        'status',
        'is_eligible_gmv_max',
        'region',
        'currency',
        'advertiser_id',
        'advertiser_account_id',
        'exclusive_authorization_status',
        // Business Center fields
        'store_authorized_bc_id',
        'is_owner_bc',
        'bc_id',
        'bc_name',
        'bc_profile_image',
        'user_role',
        // Shop details
        'store_code',
        'thumbnail_url',
        'store_role',
        // Advertiser info
        'advertiser_name',
        'targeting_region_codes',
        // Local image paths
        'thumbnail_local_path',
        'bc_profile_image_local_path',
        'images_last_synced_at',
        'image_sync_metadata',
    ];

    /**
     * Các trường cần cast kiểu dữ liệu
     */
    protected $casts = [
        'is_eligible_gmv_max' => 'boolean',
        'is_owner_bc' => 'boolean',
        'targeting_region_codes' => 'array',
        'image_sync_metadata' => 'array',
        'images_last_synced_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Relationship: Shop có nhiều Campaigns
     */
    public function campaigns(): HasMany
    {
        return $this->hasMany(Campaign::class);
    }

    /**
     * Relationship: Shop có nhiều Products
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Relationship: Shop có nhiều ExclusiveAuthorizations
     */
    public function exclusiveAuthorizations(): HasMany
    {
        return $this->hasMany(ExclusiveAuthorization::class);
    }

    /**
     * Relationship: Shop thuộc về một AdvertiserAccount
     * Sử dụng advertiser_account_id foreign key
     */
    public function advertiserAccount(): BelongsTo
    {
        return $this->belongsTo(AdvertiserAccount::class, 'advertiser_account_id', 'id');
    }

    /**
     * Alternative relationship: Shop có thể liên kết với AdvertiserAccount qua advertiser_id
     * Dùng khi cần tìm advertiser account dựa trên advertiser_id từ TikTok API
     */
    public function advertiserAccountByAdvertiserId(): BelongsTo
    {
        return $this->belongsTo(AdvertiserAccount::class, 'advertiser_id', 'advertiser_id');
    }

    /**
     * Scope: Chỉ lấy shops đủ điều kiện GMV Max
     */
    public function scopeEligibleForGmvMax($query)
    {
        return $query->where('is_eligible_gmv_max', true);
    }

    /**
     * Scope: Lọc theo trạng thái
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope: Lọc theo khu vực
     */
    public function scopeByRegion($query, $region)
    {
        return $query->where('region', $region);
    }

    /**
     * Accessor: Lấy tên hiển thị với region
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->name . ' (' . $this->region . ')';
    }

    /**
     * Business method: Kiểm tra shop có active campaigns không
     */
    public function hasActiveCampaigns(): bool
    {
        return $this->campaigns()->where('status', 'active')->exists();
    }

    /**
     * Business method: Đếm số campaigns theo trạng thái
     */
    public function getCampaignCountByStatus(string $status): int
    {
        return $this->campaigns()->where('status', $status)->count();
    }

    /**
     * Get thumbnail image URL (local first, fallback to remote)
     */
    public function getDisplayThumbnailAttribute(): ?string
    {
        if ($this->thumbnail_local_path && \Storage::disk('public')->exists($this->thumbnail_local_path)) {
            return \Storage::disk('public')->url($this->thumbnail_local_path);
        }

        return $this->thumbnail_url;
    }

    /**
     * Get BC profile image URL (local first, fallback to remote)
     */
    public function getDisplayBcProfileImageAttribute(): ?string
    {
        if ($this->bc_profile_image_local_path && \Storage::disk('public')->exists($this->bc_profile_image_local_path)) {
            return \Storage::disk('public')->url($this->bc_profile_image_local_path);
        }

        return $this->bc_profile_image;
    }

    /**
     * Check if images need to be synced
     */
    public function needsImageSync(): bool
    {
        // Sync if never synced before
        if (!$this->images_last_synced_at) {
            return true;
        }

        // Sync if last sync was more than 7 days ago
        return $this->images_last_synced_at->lt(now()->subDays(7));
    }
}
